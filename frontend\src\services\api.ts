import axios, { AxiosInstance, AxiosResponse } from 'axios'
import { ApiResponse, PaginatedResponse } from '../types'

// 创建axios实例
const api: AxiosInstance = axios.create({
  baseURL: '/api/v1',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = localStorage.getItem('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  (error) => {
    if (error.response?.status === 401) {
      // 未授权，清除token并重定向到登录页
      localStorage.removeItem('access_token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// API服务类
export class ApiService {
  // 通用GET请求
  static async get<T>(url: string, params?: any): Promise<ApiResponse<T>> {
    const response = await api.get(url, { params })
    return response.data
  }

  // 通用POST请求
  static async post<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    const response = await api.post(url, data)
    return response.data
  }

  // 通用PUT请求
  static async put<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    const response = await api.put(url, data)
    return response.data
  }

  // 通用DELETE请求
  static async delete<T>(url: string): Promise<ApiResponse<T>> {
    const response = await api.delete(url)
    return response.data
  }

  // 分页查询
  static async getPaginated<T>(
    url: string,
    page: number = 1,
    pageSize: number = 20,
    params?: any
  ): Promise<PaginatedResponse<T>> {
    const response = await api.get(url, {
      params: {
        page,
        page_size: pageSize,
        ...params,
      },
    })
    return response.data
  }

  // 文件上传
  static async uploadFile(
    url: string,
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<ApiResponse<any>> {
    const formData = new FormData()
    formData.append('file', file)

    const response = await api.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          )
          onProgress(progress)
        }
      },
    })

    return response.data
  }

  // 文件下载
  static async downloadFile(url: string, filename?: string): Promise<void> {
    const response = await api.get(url, {
      responseType: 'blob',
    })

    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
  }
}

// 认证相关API
export class AuthAPI {
  static async login(username: string, password: string) {
    return ApiService.post('/auth/login', { username, password })
  }

  static async logout() {
    return ApiService.post('/auth/logout')
  }

  static async getCurrentUser() {
    return ApiService.get('/auth/me')
  }

  static async refreshToken() {
    return ApiService.post('/auth/refresh')
  }
}

// 项目相关API
export class ProjectAPI {
  static async getProjects(page?: number, pageSize?: number) {
    return ApiService.getPaginated('/projects', page, pageSize)
  }

  static async getProject(id: number) {
    return ApiService.get(`/projects/${id}`)
  }

  static async createProject(data: any) {
    return ApiService.post('/projects', data)
  }

  static async updateProject(id: number, data: any) {
    return ApiService.put(`/projects/${id}`, data)
  }

  static async deleteProject(id: number) {
    return ApiService.delete(`/projects/${id}`)
  }
}

// 道路相关API
export class RoadAPI {
  static async getRoads(projectId: number) {
    return ApiService.get(`/projects/${projectId}/roads`)
  }

  static async getRoad(projectId: number, roadId: number) {
    return ApiService.get(`/projects/${projectId}/roads/${roadId}`)
  }

  static async createRoad(projectId: number, data: any) {
    return ApiService.post(`/projects/${projectId}/roads`, data)
  }

  static async updateRoad(projectId: number, roadId: number, data: any) {
    return ApiService.put(`/projects/${projectId}/roads/${roadId}`, data)
  }

  static async deleteRoad(projectId: number, roadId: number) {
    return ApiService.delete(`/projects/${projectId}/roads/${roadId}`)
  }

  static async analyzeRoad(projectId: number, roadId: number) {
    return ApiService.post(`/projects/${projectId}/roads/${roadId}/analyze`)
  }

  static async optimizeRoad(projectId: number, roadId: number, params: any) {
    return ApiService.post(`/projects/${projectId}/roads/${roadId}/optimize`, params)
  }
}

// 地形相关API
export class TerrainAPI {
  static async getTerrainData(projectId: number) {
    return ApiService.get(`/projects/${projectId}/terrain`)
  }

  static async uploadTerrainData(projectId: number, file: File, onProgress?: (progress: number) => void) {
    return ApiService.uploadFile(`/projects/${projectId}/terrain/upload`, file, onProgress)
  }

  static async analyzeSlope(projectId: number, params: any) {
    return ApiService.post(`/projects/${projectId}/terrain/analyze/slope`, params)
  }

  static async analyzeAspect(projectId: number, params: any) {
    return ApiService.post(`/projects/${projectId}/terrain/analyze/aspect`, params)
  }

  static async calculateVolume(projectId: number, params: any) {
    return ApiService.post(`/projects/${projectId}/terrain/analyze/volume`, params)
  }
}

// 文件上传API
export class UploadAPI {
  static async uploadFile(file: File, type: string, projectId?: number, onProgress?: (progress: number) => void) {
    const url = projectId ? `/projects/${projectId}/uploads` : '/uploads'
    const formData = new FormData()
    formData.append('file', file)
    formData.append('type', type)
    
    return ApiService.uploadFile(url, file, onProgress)
  }

  static async getUploads(projectId?: number) {
    const url = projectId ? `/projects/${projectId}/uploads` : '/uploads'
    return ApiService.get(url)
  }

  static async deleteUpload(uploadId: number) {
    return ApiService.delete(`/uploads/${uploadId}`)
  }
}

export default api
