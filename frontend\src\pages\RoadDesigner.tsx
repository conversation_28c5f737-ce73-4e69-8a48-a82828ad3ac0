import React, { useRef, useState } from 'react'
import {
  Play,
  Pause,
  RotateCcw,
  Layers,
  MapPin,
  BarChart3
} from 'lucide-react'
import { CesiumViewer } from '../components/CesiumViewer'
import { RoadDesignTools } from '../components/RoadDesignTools'
import { RoadAnalysisPanel } from '../components/RoadAnalysisPanel'
import { InteractiveToolbar } from '../components/InteractiveToolbar'
import { RealTimePreview } from '../components/RealTimePreview'
import { useRoadStore } from '../stores/useRoadStore'
import { CesiumService } from '../services/cesiumService'

const RoadDesigner: React.FC = () => {
  const cesiumServiceRef = useRef<CesiumService | null>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentCoordinate, setCurrentCoordinate] = useState<{longitude: number, latitude: number, elevation?: number} | null>(null)

  const { drawingCoordinates, analysisResult } = useRoadStore()

  const handleViewerReady = (viewer: any) => {
    cesiumServiceRef.current = new CesiumService(viewer)
  }

  const handleCoordinateClick = (coordinate: {longitude: number, latitude: number, elevation?: number}) => {
    setCurrentCoordinate(coordinate)
  }

  const handleResetView = () => {
    if (cesiumServiceRef.current) {
      cesiumServiceRef.current.setView(116.4, 39.9, 2000, 0, -45, 0)
    }
  }

  return (
    <div className="h-full flex">
      {/* 左侧工具面板 */}
      <div className="w-80 bg-dark-800 border-r border-gray-700 flex flex-col overflow-y-auto">
        {/* 设计工具 */}
        <div className="p-4">
          <RoadDesignTools />
        </div>

        {/* 图层控制 */}
        <div className="p-4 border-t border-gray-700">
          <h4 className="text-md font-medium text-white mb-3 flex items-center">
            <Layers className="w-4 h-4 mr-2" />
            图层管理
          </h4>
          <div className="space-y-2">
            {[
              { name: '地形', visible: true },
              { name: '卫星影像', visible: true },
              { name: '等高线', visible: false },
              { name: '现有道路', visible: true },
              { name: '设计道路', visible: true },
              { name: '标注', visible: true }
            ].map((layer, index) => (
              <label key={index} className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={layer.visible}
                  className="w-4 h-4 text-primary-500 bg-dark-700 border-gray-600 rounded focus:ring-primary-500"
                />
                <span className="text-sm text-gray-300">{layer.name}</span>
              </label>
            ))}
          </div>
        </div>

        {/* 分析结果 */}
        <div className="p-4 border-t border-gray-700">
          <RoadAnalysisPanel />
        </div>

        {/* 当前坐标 */}
        {currentCoordinate && (
          <div className="p-4 border-t border-gray-700">
            <h4 className="text-md font-medium text-white mb-3 flex items-center">
              <MapPin className="w-4 h-4 mr-2" />
              当前位置
            </h4>
            <div className="bg-dark-700 rounded-lg p-3 space-y-1 text-xs font-mono">
              <div>经度: {currentCoordinate.longitude.toFixed(6)}°</div>
              <div>纬度: {currentCoordinate.latitude.toFixed(6)}°</div>
              {currentCoordinate.elevation && (
                <div>高程: {currentCoordinate.elevation.toFixed(1)}m</div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* 主要设计区域 */}
      <div className="flex-1 relative">
        {/* 交互式工具栏 */}
        <div className="absolute top-4 left-4 z-10">
          <InteractiveToolbar
            onToolChange={(toolId) => {
              // 工具栏工具变更处理
              console.log('工具变更:', toolId)
            }}
            onViewAction={(action) => {
              // 视图操作处理
              if (action === 'reset-view') {
                handleResetView()
              }
              console.log('视图操作:', action)
            }}
          />
        </div>

        {/* 状态信息栏 */}
        {currentCoordinate && (
          <div className="absolute top-4 right-4 z-10">
            <div className="bg-dark-800/90 backdrop-blur-sm border border-gray-700 rounded-lg px-3 py-2">
              <div className="flex items-center space-x-4 text-sm text-gray-300">
                <div className="flex items-center space-x-2">
                  <MapPin className="w-4 h-4" />
                  <span className="font-mono">
                    {currentCoordinate.longitude.toFixed(4)}°E, {currentCoordinate.latitude.toFixed(4)}°N
                  </span>
                </div>

                {currentCoordinate.elevation && (
                  <div className="flex items-center space-x-2">
                    <span>高程:</span>
                    <span className="font-mono">{currentCoordinate.elevation.toFixed(1)}m</span>
                  </div>
                )}

                {drawingCoordinates.length > 0 && (
                  <div className="flex items-center space-x-2 text-primary-400">
                    <span>点数:</span>
                    <span className="font-mono">{drawingCoordinates.length}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Cesium 3D视图 */}
        <CesiumViewer
          className="w-full h-full"
          onViewerReady={handleViewerReady}
          onCoordinateClick={handleCoordinateClick}
        />

        {/* 实时预览面板 */}
        <RealTimePreview />
      </div>
    </div>
  )
}

export default RoadDesigner
