import React, { useState, useEffect } from 'react'
import {
  Bar<PERSON>hart3,
  <PERSON><PERSON>dingUp,
  AlertTriangle,
  CheckCircle,
  Info,
  Calculator,
  Download,
  RefreshCw
} from 'lucide-react'
import { useRoadStore } from '../stores/useRoadStore'
import { RoadAnalysisService } from '../services/roadAnalysisService'
import { Button } from './ui/Button'
import { Modal, ModalBody, ModalFooter, ModalHeader } from './ui/Modal'

export const RoadAnalysisPanel: React.FC = () => {
  const { drawingCoordinates, designParams, analysisResult, setAnalysisResult } = useRoadStore()
  const [showDetailedReport, setShowDetailedReport] = useState(false)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [detailedAnalysis, setDetailedAnalysis] = useState<any>(null)

  // 实时分析
  useEffect(() => {
    if (drawingCoordinates.length >= 2) {
      performAnalysis()
    } else {
      setAnalysisResult(null)
    }
  }, [drawingCoordinates, designParams])

  const performAnalysis = async () => {
    if (drawingCoordinates.length < 2) return

    setIsAnalyzing(true)
    try {
      // 执行道路分析
      const analysis = RoadAnalysisService.generateAnalysisReport(
        drawingCoordinates,
        designParams
      )
      
      setAnalysisResult(analysis)

      // 生成详细分析
      const validation = RoadAnalysisService.validateDesign(drawingCoordinates, designParams)
      const earthwork = RoadAnalysisService.calculateEarthwork(drawingCoordinates, designParams.width)
      
      setDetailedAnalysis({
        validation,
        earthwork,
        geometry: validation.analysis
      })
    } catch (error) {
      console.error('分析失败:', error)
    } finally {
      setIsAnalyzing(false)
    }
  }

  const handleExportReport = () => {
    if (!analysisResult || !detailedAnalysis) return

    const reportData = {
      基本信息: {
        总长度: `${analysisResult.totalLength.toFixed(1)} m`,
        道路宽度: `${designParams.width} m`,
        路面类型: designParams.surfaceType,
        设计速度: `${designParams.designSpeed} km/h`
      },
      几何特性: {
        平均坡度: `${analysisResult.averageGrade.toFixed(1)}%`,
        最大坡度: `${analysisResult.maxGrade.toFixed(1)}%`,
        最小坡度: `${analysisResult.minGrade.toFixed(1)}%`,
        急弯数量: analysisResult.sharpCurves
      },
      土方工程: {
        挖方量: `${analysisResult.earthworkVolume.cut.toFixed(1)} m³`,
        填方量: `${analysisResult.earthworkVolume.fill.toFixed(1)} m³`,
        净土方量: `${(analysisResult.earthworkVolume.cut - analysisResult.earthworkVolume.fill).toFixed(1)} m³`
      },
      成本估算: {
        总成本: `¥${analysisResult.estimatedCost.toLocaleString()}`,
        单位成本: `¥${(analysisResult.estimatedCost / analysisResult.totalLength).toFixed(0)}/m`,
        施工时间: `${analysisResult.constructionTime} 天`
      }
    }

    const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `道路分析报告_${new Date().toISOString().split('T')[0]}.json`
    a.click()
    URL.revokeObjectURL(url)
  }

  if (!analysisResult) {
    return (
      <div className="bg-dark-800 border border-gray-700 rounded-lg p-4">
        <div className="flex items-center space-x-2 text-gray-400">
          <BarChart3 className="w-5 h-5" />
          <span className="text-sm">绘制道路以查看分析结果</span>
        </div>
      </div>
    )
  }

  return (
    <>
      <div className="bg-dark-800 border border-gray-700 rounded-lg p-4 space-y-4">
        {/* 标题和操作 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <BarChart3 className="w-5 h-5 text-primary-400" />
            <h3 className="text-lg font-semibold text-white">分析结果</h3>
          </div>
          <div className="flex space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={performAnalysis}
              loading={isAnalyzing}
            >
              <RefreshCw className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowDetailedReport(true)}
            >
              <Info className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* 关键指标 */}
        <div className="grid grid-cols-2 gap-3">
          <div className="bg-dark-700 rounded-lg p-3">
            <div className="text-xs text-gray-400 mb-1">总长度</div>
            <div className="text-lg font-bold text-white">
              {(analysisResult.totalLength / 1000).toFixed(2)} km
            </div>
          </div>
          
          <div className="bg-dark-700 rounded-lg p-3">
            <div className="text-xs text-gray-400 mb-1">平均坡度</div>
            <div className="text-lg font-bold text-white">
              {analysisResult.averageGrade.toFixed(1)}%
            </div>
          </div>
          
          <div className="bg-dark-700 rounded-lg p-3">
            <div className="text-xs text-gray-400 mb-1">最大坡度</div>
            <div className={`text-lg font-bold ${
              analysisResult.maxGrade > designParams.maxGrade ? 'text-red-400' : 'text-white'
            }`}>
              {analysisResult.maxGrade.toFixed(1)}%
            </div>
          </div>
          
          <div className="bg-dark-700 rounded-lg p-3">
            <div className="text-xs text-gray-400 mb-1">急弯数量</div>
            <div className={`text-lg font-bold ${
              analysisResult.sharpCurves > 0 ? 'text-yellow-400' : 'text-white'
            }`}>
              {analysisResult.sharpCurves}
            </div>
          </div>
        </div>

        {/* 验证状态 */}
        {detailedAnalysis?.validation && (
          <div className="space-y-2">
            {detailedAnalysis.validation.violations.length > 0 && (
              <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-3">
                <div className="flex items-center space-x-2 mb-2">
                  <AlertTriangle className="w-4 h-4 text-red-400" />
                  <span className="text-sm font-medium text-red-400">设计违规</span>
                </div>
                <ul className="text-xs text-red-300 space-y-1">
                  {detailedAnalysis.validation.violations.map((violation: string, index: number) => (
                    <li key={index}>• {violation}</li>
                  ))}
                </ul>
              </div>
            )}

            {detailedAnalysis.validation.warnings.length > 0 && (
              <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-3">
                <div className="flex items-center space-x-2 mb-2">
                  <AlertTriangle className="w-4 h-4 text-yellow-400" />
                  <span className="text-sm font-medium text-yellow-400">设计警告</span>
                </div>
                <ul className="text-xs text-yellow-300 space-y-1">
                  {detailedAnalysis.validation.warnings.map((warning: string, index: number) => (
                    <li key={index}>• {warning}</li>
                  ))}
                </ul>
              </div>
            )}

            {detailedAnalysis.validation.valid && (
              <div className="bg-green-500/10 border border-green-500/30 rounded-lg p-3">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <span className="text-sm font-medium text-green-400">设计符合规范</span>
                </div>
              </div>
            )}
          </div>
        )}

        {/* 成本估算 */}
        <div className="bg-dark-700 rounded-lg p-3">
          <div className="flex items-center space-x-2 mb-2">
            <Calculator className="w-4 h-4 text-primary-400" />
            <span className="text-sm font-medium text-white">成本估算</span>
          </div>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="flex justify-between">
              <span className="text-gray-400">总成本:</span>
              <span className="text-white">¥{analysisResult.estimatedCost.toLocaleString()}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">施工时间:</span>
              <span className="text-white">{analysisResult.constructionTime} 天</span>
            </div>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex space-x-2">
          <Button
            variant="secondary"
            size="sm"
            onClick={() => setShowDetailedReport(true)}
            className="flex-1"
          >
            <TrendingUp className="w-4 h-4 mr-2" />
            详细报告
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleExportReport}
          >
            <Download className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* 详细报告模态框 */}
      <Modal
        isOpen={showDetailedReport}
        onClose={() => setShowDetailedReport(false)}
        title="详细分析报告"
        size="xl"
      >
        <ModalBody>
          {detailedAnalysis && (
            <div className="space-y-6">
              {/* 几何分析 */}
              <div>
                <h4 className="text-lg font-semibold text-white mb-3">几何分析</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-dark-700 rounded-lg p-4">
                    <h5 className="text-sm font-medium text-gray-300 mb-2">坡度分布</h5>
                    <div className="space-y-2">
                      {Object.entries(detailedAnalysis.geometry.grades.distribution).map(([range, count]) => (
                        <div key={range} className="flex justify-between text-xs">
                          <span className="text-gray-400">{range}:</span>
                          <span className="text-white">{count} 段</span>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div className="bg-dark-700 rounded-lg p-4">
                    <h5 className="text-sm font-medium text-gray-300 mb-2">曲线统计</h5>
                    <div className="space-y-2 text-xs">
                      <div className="flex justify-between">
                        <span className="text-gray-400">总曲线数:</span>
                        <span className="text-white">{detailedAnalysis.geometry.curves.length}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">左转弯:</span>
                        <span className="text-white">
                          {detailedAnalysis.geometry.curves.filter((c: any) => c.type === 'left').length}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">右转弯:</span>
                        <span className="text-white">
                          {detailedAnalysis.geometry.curves.filter((c: any) => c.type === 'right').length}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* 土方工程 */}
              <div>
                <h4 className="text-lg font-semibold text-white mb-3">土方工程</h4>
                <div className="bg-dark-700 rounded-lg p-4">
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="text-2xl font-bold text-red-400">
                        {detailedAnalysis.earthwork.cutVolume.toFixed(0)}
                      </div>
                      <div className="text-xs text-gray-400">挖方 (m³)</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-green-400">
                        {detailedAnalysis.earthwork.fillVolume.toFixed(0)}
                      </div>
                      <div className="text-xs text-gray-400">填方 (m³)</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-primary-400">
                        {Math.abs(detailedAnalysis.earthwork.netVolume).toFixed(0)}
                      </div>
                      <div className="text-xs text-gray-400">
                        净{detailedAnalysis.earthwork.netVolume > 0 ? '填' : '挖'}方 (m³)
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </ModalBody>

        <ModalFooter>
          <Button variant="ghost" onClick={() => setShowDetailedReport(false)}>
            关闭
          </Button>
          <Button variant="primary" onClick={handleExportReport}>
            <Download className="w-4 h-4 mr-2" />
            导出报告
          </Button>
        </ModalFooter>
      </Modal>
    </>
  )
}
