"""
数据管理服务
"""

import json
import os
import shutil
from datetime import datetime
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session
from sqlalchemy import desc

from app.models.project import Project
from app.models.road import Road
from app.models.terrain import TerrainData, ProjectSnapshot, FileUpload
from app.models.user import User
from app.core.config import settings


class DataManagerService:
    """数据管理服务"""
    
    @staticmethod
    def create_project_snapshot(
        db: Session,
        project_id: int,
        user_id: int,
        name: str,
        description: Optional[str] = None,
        is_auto_save: bool = False
    ) -> ProjectSnapshot:
        """创建项目快照"""
        
        # 获取项目数据
        project = db.query(Project).filter(Project.id == project_id).first()
        if not project:
            raise ValueError("项目不存在")
        
        # 获取道路数据
        roads = db.query(Road).filter(Road.project_id == project_id).all()
        roads_data = [
            {
                "id": road.id,
                "name": road.name,
                "type": road.type.value,
                "width": road.width,
                "length": road.length,
                "max_grade": road.max_grade,
                "min_radius": road.min_radius,
                "surface_type": road.surface_type.value,
                "coordinates": road.coordinates,
                "elevation_profile": road.elevation_profile,
                "design_speed": road.design_speed,
                "load_capacity": road.load_capacity,
                "status": road.status.value
            }
            for road in roads
        ]
        
        # 获取地形数据引用
        terrain_data = db.query(TerrainData).filter(TerrainData.project_id == project_id).all()
        terrain_refs = [
            {
                "id": terrain.id,
                "name": terrain.name,
                "type": terrain.type.value,
                "format": terrain.format.value,
                "file_path": terrain.file_path,
                "extent": terrain.extent
            }
            for terrain in terrain_data
        ]
        
        # 序列化项目数据
        project_data = {
            "id": project.id,
            "name": project.name,
            "description": project.description,
            "location": project.location,
            "status": project.status.value,
            "progress": project.progress,
            "created_at": project.created_at.isoformat(),
            "updated_at": project.updated_at.isoformat()
        }
        
        # 生成版本号
        latest_snapshot = db.query(ProjectSnapshot)\
            .filter(ProjectSnapshot.project_id == project_id)\
            .order_by(desc(ProjectSnapshot.created_at))\
            .first()
        
        if latest_snapshot:
            # 解析版本号并递增
            try:
                version_parts = latest_snapshot.version.split('.')
                major, minor = int(version_parts[0]), int(version_parts[1])
                if is_auto_save:
                    minor += 1
                else:
                    major += 1
                    minor = 0
                version = f"{major}.{minor}"
            except:
                version = "1.0"
        else:
            version = "1.0"
        
        # 创建快照
        snapshot = ProjectSnapshot(
            name=name,
            description=description,
            version=version,
            project_data=project_data,
            roads_data=roads_data,
            terrain_data_refs=terrain_refs,
            is_auto_save="true" if is_auto_save else "false",
            project_id=project_id,
            created_by=user_id
        )
        
        db.add(snapshot)
        db.commit()
        db.refresh(snapshot)
        
        return snapshot
    
    @staticmethod
    def restore_project_snapshot(
        db: Session,
        snapshot_id: int,
        user_id: int
    ) -> Project:
        """恢复项目快照"""
        
        snapshot = db.query(ProjectSnapshot).filter(ProjectSnapshot.id == snapshot_id).first()
        if not snapshot:
            raise ValueError("快照不存在")
        
        project = db.query(Project).filter(Project.id == snapshot.project_id).first()
        if not project:
            raise ValueError("项目不存在")
        
        # 创建当前状态的备份快照
        DataManagerService.create_project_snapshot(
            db, project.id, user_id, 
            f"恢复前备份 - {datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "恢复快照前的自动备份",
            is_auto_save=True
        )
        
        # 恢复项目基本信息
        project_data = snapshot.project_data
        project.name = project_data.get("name", project.name)
        project.description = project_data.get("description", project.description)
        project.location = project_data.get("location", project.location)
        
        # 删除现有道路数据
        db.query(Road).filter(Road.project_id == project.id).delete()
        
        # 恢复道路数据
        if snapshot.roads_data:
            for road_data in snapshot.roads_data:
                road = Road(
                    name=road_data["name"],
                    type=road_data["type"],
                    width=road_data["width"],
                    length=road_data["length"],
                    max_grade=road_data["max_grade"],
                    min_radius=road_data["min_radius"],
                    surface_type=road_data["surface_type"],
                    coordinates=road_data["coordinates"],
                    elevation_profile=road_data.get("elevation_profile"),
                    design_speed=road_data.get("design_speed", 30.0),
                    load_capacity=road_data.get("load_capacity", 50.0),
                    status=road_data.get("status", "draft"),
                    project_id=project.id
                )
                db.add(road)
        
        db.commit()
        db.refresh(project)
        
        return project
    
    @staticmethod
    def export_project_data(
        db: Session,
        project_id: int,
        export_format: str = "json"
    ) -> Dict[str, Any]:
        """导出项目数据"""
        
        project = db.query(Project).filter(Project.id == project_id).first()
        if not project:
            raise ValueError("项目不存在")
        
        # 获取完整项目数据
        roads = db.query(Road).filter(Road.project_id == project_id).all()
        terrain_data = db.query(TerrainData).filter(TerrainData.project_id == project_id).all()
        uploads = db.query(FileUpload).filter(FileUpload.project_id == project_id).all()
        
        export_data = {
            "project": {
                "id": project.id,
                "name": project.name,
                "description": project.description,
                "location": project.location,
                "status": project.status.value,
                "progress": project.progress,
                "created_at": project.created_at.isoformat(),
                "updated_at": project.updated_at.isoformat()
            },
            "roads": [
                {
                    "id": road.id,
                    "name": road.name,
                    "type": road.type.value,
                    "width": road.width,
                    "length": road.length,
                    "max_grade": road.max_grade,
                    "min_radius": road.min_radius,
                    "surface_type": road.surface_type.value,
                    "coordinates": road.coordinates,
                    "elevation_profile": road.elevation_profile,
                    "design_speed": road.design_speed,
                    "load_capacity": road.load_capacity,
                    "status": road.status.value,
                    "created_at": road.created_at.isoformat(),
                    "updated_at": road.updated_at.isoformat()
                }
                for road in roads
            ],
            "terrain_data": [
                {
                    "id": terrain.id,
                    "name": terrain.name,
                    "type": terrain.type.value,
                    "format": terrain.format.value,
                    "resolution": terrain.resolution,
                    "extent": terrain.extent,
                    "min_elevation": terrain.min_elevation,
                    "max_elevation": terrain.max_elevation,
                    "coordinate_system": terrain.coordinate_system
                }
                for terrain in terrain_data
            ],
            "files": [
                {
                    "id": upload.id,
                    "filename": upload.filename,
                    "original_name": upload.original_name,
                    "file_type": upload.file_type,
                    "file_size": upload.file_size,
                    "uploaded_at": upload.created_at.isoformat()
                }
                for upload in uploads
            ],
            "export_info": {
                "exported_at": datetime.now().isoformat(),
                "format": export_format,
                "version": "1.0"
            }
        }
        
        return export_data
    
    @staticmethod
    def import_project_data(
        db: Session,
        import_data: Dict[str, Any],
        user_id: int,
        overwrite: bool = False
    ) -> Project:
        """导入项目数据"""
        
        project_data = import_data.get("project", {})
        
        # 检查项目是否已存在
        existing_project = None
        if "id" in project_data:
            existing_project = db.query(Project).filter(Project.id == project_data["id"]).first()
        
        if existing_project and not overwrite:
            raise ValueError("项目已存在，请选择覆盖或创建新项目")
        
        # 创建或更新项目
        if existing_project and overwrite:
            project = existing_project
            project.name = project_data.get("name", project.name)
            project.description = project_data.get("description", project.description)
            project.location = project_data.get("location", project.location)
        else:
            project = Project(
                name=project_data.get("name", "导入的项目"),
                description=project_data.get("description"),
                location=project_data.get("location", "未知位置"),
                creator_id=user_id
            )
            db.add(project)
            db.flush()  # 获取项目ID
        
        # 导入道路数据
        roads_data = import_data.get("roads", [])
        for road_data in roads_data:
            road = Road(
                name=road_data.get("name", "导入的道路"),
                type=road_data.get("type", "secondary"),
                width=road_data.get("width", 6.0),
                length=road_data.get("length", 0.0),
                max_grade=road_data.get("max_grade", 8.0),
                min_radius=road_data.get("min_radius", 25.0),
                surface_type=road_data.get("surface_type", "gravel"),
                coordinates=road_data.get("coordinates"),
                elevation_profile=road_data.get("elevation_profile"),
                design_speed=road_data.get("design_speed", 30.0),
                load_capacity=road_data.get("load_capacity", 50.0),
                status=road_data.get("status", "draft"),
                project_id=project.id
            )
            db.add(road)
        
        db.commit()
        db.refresh(project)
        
        return project
    
    @staticmethod
    def cleanup_old_snapshots(
        db: Session,
        project_id: int,
        keep_count: int = 10
    ) -> int:
        """清理旧快照，保留最新的指定数量"""
        
        snapshots = db.query(ProjectSnapshot)\
            .filter(ProjectSnapshot.project_id == project_id)\
            .order_by(desc(ProjectSnapshot.created_at))\
            .all()
        
        if len(snapshots) <= keep_count:
            return 0
        
        # 删除多余的快照
        snapshots_to_delete = snapshots[keep_count:]
        deleted_count = 0
        
        for snapshot in snapshots_to_delete:
            db.delete(snapshot)
            deleted_count += 1
        
        db.commit()
        return deleted_count
    
    @staticmethod
    def get_project_statistics(db: Session, project_id: int) -> Dict[str, Any]:
        """获取项目统计信息"""
        
        project = db.query(Project).filter(Project.id == project_id).first()
        if not project:
            raise ValueError("项目不存在")
        
        roads_count = db.query(Road).filter(Road.project_id == project_id).count()
        terrain_count = db.query(TerrainData).filter(TerrainData.project_id == project_id).count()
        uploads_count = db.query(FileUpload).filter(FileUpload.project_id == project_id).count()
        snapshots_count = db.query(ProjectSnapshot).filter(ProjectSnapshot.project_id == project_id).count()
        
        # 计算总道路长度
        roads = db.query(Road).filter(Road.project_id == project_id).all()
        total_length = sum(road.length for road in roads)
        
        # 计算文件总大小
        uploads = db.query(FileUpload).filter(FileUpload.project_id == project_id).all()
        total_file_size = sum(upload.file_size for upload in uploads)
        
        return {
            "roads_count": roads_count,
            "terrain_data_count": terrain_count,
            "uploads_count": uploads_count,
            "snapshots_count": snapshots_count,
            "total_road_length": total_length,
            "total_file_size": total_file_size,
            "project_age_days": (datetime.now() - project.created_at).days,
            "last_modified": project.updated_at.isoformat()
        }
