<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>启动 - 露天矿山道路设计系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 600px;
            margin: 20px;
        }
        
        .logo {
            font-size: 48px;
            margin-bottom: 20px;
        }
        
        .title {
            font-size: 28px;
            color: #333;
            margin-bottom: 10px;
            font-weight: bold;
        }
        
        .subtitle {
            font-size: 16px;
            color: #666;
            margin-bottom: 30px;
        }
        
        .launch-btn {
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            color: white;
            border: none;
            padding: 20px 40px;
            font-size: 20px;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
            font-weight: bold;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        
        .launch-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }
        
        .features {
            margin-top: 30px;
            text-align: left;
        }
        
        .feature {
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .feature-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .feature-desc {
            color: #666;
            font-size: 14px;
        }
        
        .instructions {
            margin-top: 30px;
            padding: 20px;
            background: #e3f2fd;
            border-radius: 10px;
            border: 1px solid #2196f3;
        }
        
        .instructions h3 {
            color: #1976d2;
            margin-top: 0;
        }
        
        .step {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border-left: 3px solid #2196f3;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .warning-title {
            color: #856404;
            font-weight: bold;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🏔️</div>
        <div class="title">露天矿山道路设计系统</div>
        <div class="subtitle">专业的三维道路设计软件演示</div>
        
        <a href="simple-demo.html" class="launch-btn" onclick="trackLaunch()">
            🚀 启动演示程序
        </a>
        
        <div class="features">
            <div class="feature">
                <div class="feature-title">🛠️ 智能设计工具</div>
                <div class="feature-desc">可视化道路绘制、参数化设计、实时验证</div>
            </div>
            
            <div class="feature">
                <div class="feature-title">📊 专业分析功能</div>
                <div class="feature-desc">几何分析、土方计算、成本估算、工程报告</div>
            </div>
            
            <div class="feature">
                <div class="feature-title">🗺️ 三维可视化</div>
                <div class="feature-desc">基于Cesium的高性能三维地球引擎</div>
            </div>
            
            <div class="feature">
                <div class="feature-title">💾 项目管理</div>
                <div class="feature-desc">版本控制、数据导入导出、协作设计</div>
            </div>
        </div>
        
        <div class="instructions">
            <h3>📋 使用说明</h3>
            
            <div class="step">
                <strong>步骤1:</strong> 点击上方"启动演示程序"按钮
            </div>
            
            <div class="step">
                <strong>步骤2:</strong> 如果没有自动打开，请手动打开 <code>simple-demo.html</code> 文件
            </div>
            
            <div class="step">
                <strong>步骤3:</strong> 在演示页面中体验各种设计工具和分析功能
            </div>
            
            <div class="step">
                <strong>步骤4:</strong> 查看 <code>如何运行程序.txt</code> 获取详细说明
            </div>
        </div>
        
        <div class="warning">
            <div class="warning-title">⚠️ 重要提示</div>
            <div>
                • 当前是演示版本，展示完整的界面设计和交互功能<br>
                • 要使用完整的3D地形和数据库功能，请参考 <code>INSTALLATION.md</code><br>
                • 建议使用Chrome、Edge或Firefox浏览器获得最佳体验
            </div>
        </div>
        
        <div style="margin-top: 30px; color: #666; font-size: 14px;">
            <p>🔧 技术栈: React + FastAPI + Cesium + PostgreSQL</p>
            <p>📅 版本: 1.0.0 演示版</p>
        </div>
    </div>
    
    <script>
        function trackLaunch() {
            console.log('用户点击了启动按钮');
            
            // 尝试在新窗口打开
            setTimeout(() => {
                const newWindow = window.open('simple-demo.html', '_blank');
                if (!newWindow) {
                    alert('请允许弹窗，或手动打开 simple-demo.html 文件');
                }
            }, 100);
        }
        
        // 页面加载完成后的提示
        window.onload = function() {
            console.log('露天矿山道路设计系统启动页面已加载');
            
            // 检查是否支持现代浏览器特性
            if (!window.fetch || !window.Promise) {
                alert('检测到您的浏览器版本较旧，建议升级到最新版本以获得最佳体验。');
            }
        };
        
        // 添加键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                document.querySelector('.launch-btn').click();
            }
        });
    </script>
</body>
</html>
