<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>露天矿山道路设计系统 - 演示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cesium.com/downloads/cesiumjs/releases/1.111/Build/Cesium/Cesium.js"></script>
    <link href="https://cesium.com/downloads/cesiumjs/releases/1.111/Build/Cesium/Widgets/widgets.css" rel="stylesheet">
    <style>
        .bg-dark-900 { background-color: #0f172a; }
        .bg-dark-800 { background-color: #1e293b; }
        .bg-dark-700 { background-color: #334155; }
        .text-primary-400 { color: #fbbf24; }
        .text-primary-500 { color: #f59e0b; }
        .border-gray-700 { border-color: #374151; }
        .border-gray-600 { border-color: #4b5563; }
        
        .cesium-viewer-toolbar,
        .cesium-viewer-animationContainer,
        .cesium-viewer-timelineContainer,
        .cesium-viewer-bottom {
            display: none !important;
        }
        
        .cesium-widget-credits {
            display: none !important;
        }
        
        .toolbar {
            display: flex;
            align-items: center;
            space-x: 1rem;
            background: rgba(30, 41, 59, 0.9);
            backdrop-filter: blur(8px);
            border: 1px solid #374151;
            border-radius: 0.5rem;
            padding: 0.5rem;
        }
        
        .panel {
            background: rgba(30, 41, 59, 0.95);
            backdrop-filter: blur(8px);
            border: 1px solid #374151;
            border-radius: 0.5rem;
            padding: 1rem;
        }
    </style>
</head>
<body class="bg-dark-900 text-white font-sans">
    <div class="h-screen flex flex-col">
        <!-- 顶部导航栏 -->
        <header class="h-16 bg-dark-800 border-b border-gray-700 flex items-center justify-between px-6">
            <div class="flex items-center space-x-4">
                <h1 class="text-xl font-bold text-primary-400">露天矿山道路设计系统</h1>
                <nav class="flex items-center space-x-2 text-sm text-gray-400">
                    <span>工作区</span>
                    <span>/</span>
                    <span class="text-gray-200">矿山A区道路设计</span>
                </nav>
            </div>
            
            <div class="flex items-center space-x-3">
                <div class="text-center">
                    <div class="text-sm font-medium text-gray-200">演示项目</div>
                    <div class="text-xs text-gray-400">最后保存: 刚刚</div>
                </div>
                <div class="w-8 h-8 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center text-white font-medium text-sm">
                    演
                </div>
            </div>
        </header>

        <div class="flex-1 flex">
            <!-- 左侧工具面板 -->
            <div class="w-80 bg-dark-800 border-r border-gray-700 flex flex-col overflow-y-auto">
                <!-- 设计工具 -->
                <div class="p-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white mb-4">设计工具</h3>
                    <div class="grid grid-cols-2 gap-2">
                        <button class="p-3 rounded-lg border bg-primary-500/20 border-primary-500 text-primary-400">
                            <svg class="w-5 h-5 mx-auto mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.122 2.122"></path>
                            </svg>
                            <div class="text-xs">选择</div>
                        </button>
                        <button class="p-3 rounded-lg border bg-dark-700 border-gray-600 text-gray-300 hover:bg-gray-600">
                            <svg class="w-5 h-5 mx-auto mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16l-4-4m0 0l4-4m-4 4h18"></path>
                            </svg>
                            <div class="text-xs">移动</div>
                        </button>
                        <button class="p-3 rounded-lg border bg-dark-700 border-gray-600 text-gray-300 hover:bg-gray-600">
                            <svg class="w-5 h-5 mx-auto mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                            </svg>
                            <div class="text-xs">绘制道路</div>
                        </button>
                        <button class="p-3 rounded-lg border bg-dark-700 border-gray-600 text-gray-300 hover:bg-gray-600">
                            <svg class="w-5 h-5 mx-auto mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21l3-3 9-9a1.5 1.5 0 00-3-3l-9 9-3 3v3h3z"></path>
                            </svg>
                            <div class="text-xs">测量</div>
                        </button>
                    </div>
                </div>

                <!-- 道路参数 -->
                <div class="p-4 border-b border-gray-700">
                    <h4 class="text-md font-medium text-white mb-3">道路参数</h4>
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm text-gray-300 mb-1">道路宽度 (m)</label>
                            <input type="number" value="6" class="w-full px-3 py-2 bg-dark-700 border border-gray-600 rounded text-gray-100" />
                        </div>
                        <div>
                            <label class="block text-sm text-gray-300 mb-1">最大坡度 (%)</label>
                            <input type="number" value="8" class="w-full px-3 py-2 bg-dark-700 border border-gray-600 rounded text-gray-100" />
                        </div>
                        <div>
                            <label class="block text-sm text-gray-300 mb-1">最小转弯半径 (m)</label>
                            <input type="number" value="25" class="w-full px-3 py-2 bg-dark-700 border border-gray-600 rounded text-gray-100" />
                        </div>
                    </div>
                </div>

                <!-- 分析结果 */
                <div class="p-4 border-b border-gray-700">
                    <h4 class="text-md font-medium text-white mb-3 flex items-center">
                        <svg class="w-4 h-4 mr-2 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        分析结果
                    </h4>
                    <div class="bg-dark-700 rounded-lg p-3 space-y-2 text-xs">
                        <div class="flex justify-between">
                            <span class="text-gray-400">总长度:</span>
                            <span class="text-white">2.45 km</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">平均坡度:</span>
                            <span class="text-white">5.2%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">最大坡度:</span>
                            <span class="text-white">7.8%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">估算成本:</span>
                            <span class="text-white">¥1,250,000</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 主要设计区域 */
            <div class="flex-1 relative">
                <!-- 顶部工具栏 -->
                <div class="absolute top-4 left-4 z-10">
                    <div class="toolbar">
                        <button class="p-2 rounded-lg bg-gray-700 text-gray-300 hover:bg-gray-600 transition-colors" title="选择工具">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.122 2.122"></path>
                            </svg>
                        </button>
                        <button class="p-2 rounded-lg bg-primary-500 text-white transition-colors" title="绘制道路">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                            </svg>
                        </button>
                        <button class="p-2 rounded-lg bg-gray-700 text-gray-300 hover:bg-gray-600 transition-colors" title="重置视图">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- 状态信息 -->
                <div class="absolute top-4 right-4 z-10">
                    <div class="panel">
                        <div class="text-sm text-gray-300">
                            <div class="flex items-center space-x-2 mb-1">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                <span class="font-mono">116.4°E, 39.9°N</span>
                            </div>
                            <div class="text-xs text-gray-400">高程: 1,245m</div>
                        </div>
                    </div>
                </div>

                <!-- 3D视图容器 -->
                <div id="cesiumContainer" class="w-full h-full"></div>

                <!-- 实时预览面板 -->
                <div class="absolute bottom-4 right-4 w-96 panel z-10">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center space-x-2">
                            <svg class="w-4 h-4 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            <span class="text-sm font-medium text-white">实时预览</span>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-2 text-xs mb-3">
                        <div class="bg-dark-700 rounded p-2">
                            <div class="text-gray-400">长度</div>
                            <div class="text-white font-medium">2.45 km</div>
                        </div>
                        <div class="bg-dark-700 rounded p-2">
                            <div class="text-gray-400">平均坡度</div>
                            <div class="text-white font-medium">5.2%</div>
                        </div>
                    </div>
                    
                    <div class="bg-green-500/10 border border-green-500/30 rounded p-2">
                        <div class="text-xs text-green-400 font-medium">✓ 设计符合规范</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化Cesium
        Cesium.Ion.defaultAccessToken = 'your-cesium-ion-access-token';
        
        const viewer = new Cesium.Viewer('cesiumContainer', {
            terrainProvider: Cesium.createWorldTerrain(),
            baseLayerPicker: false,
            geocoder: false,
            homeButton: false,
            sceneModePicker: false,
            navigationHelpButton: false,
            animation: false,
            timeline: false,
            fullscreenButton: false,
            vrButton: false
        });

        // 设置初始视角
        viewer.camera.setView({
            destination: Cesium.Cartesian3.fromDegrees(116.4, 39.9, 2000),
            orientation: {
                heading: Cesium.Math.toRadians(0),
                pitch: Cesium.Math.toRadians(-45),
                roll: 0.0
            }
        });

        // 添加示例道路
        const roadPositions = [
            Cesium.Cartesian3.fromDegrees(116.39, 39.89, 100),
            Cesium.Cartesian3.fromDegrees(116.40, 39.90, 120),
            Cesium.Cartesian3.fromDegrees(116.41, 39.91, 140),
            Cesium.Cartesian3.fromDegrees(116.42, 39.92, 110)
        ];

        viewer.entities.add({
            name: '示例道路',
            polyline: {
                positions: roadPositions,
                width: 8,
                material: Cesium.Color.YELLOW,
                clampToGround: true,
                outline: true,
                outlineColor: Cesium.Color.BLACK,
                outlineWidth: 2
            }
        });

        // 添加道路标注
        viewer.entities.add({
            position: roadPositions[1],
            label: {
                text: '主干道',
                font: '14pt sans-serif',
                fillColor: Cesium.Color.WHITE,
                outlineColor: Cesium.Color.BLACK,
                outlineWidth: 2,
                style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                pixelOffset: new Cesium.Cartesian2(0, -50)
            }
        });

        console.log('露天矿山道路设计系统演示已加载');
    </script>
</body>
</html>
