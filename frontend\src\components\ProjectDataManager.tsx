import React, { useState, useEffect } from 'react'
import {
  Save,
  Download,
  Upload,
  History,
  Trash2,
  FileText,
  Database,
  Clock,
  AlertCircle,
  CheckCircle
} from 'lucide-react'
import { useAppStore } from '../stores/useAppStore'
import { ProjectAPI } from '../services/api'
import { Button } from './ui/Button'
import { Input } from './ui/Input'
import { Modal, ModalBody, ModalFooter, ModalHeader } from './ui/Modal'

interface Snapshot {
  id: number
  name: string
  description: string
  version: string
  created_at: string
  is_auto_save: boolean
  changes_summary: string
}

interface ProjectStats {
  roads_count: number
  terrain_data_count: number
  uploads_count: number
  snapshots_count: number
  total_road_length: number
  total_file_size: number
  project_age_days: number
  last_modified: string
}

export const ProjectDataManager: React.FC = () => {
  const { currentProject } = useAppStore()
  const [snapshots, setSnapshots] = useState<Snapshot[]>([])
  const [projectStats, setProjectStats] = useState<ProjectStats | null>(null)
  const [showCreateSnapshot, setShowCreateSnapshot] = useState(false)
  const [showImportExport, setShowImportExport] = useState(false)
  const [loading, setLoading] = useState(false)
  
  const [snapshotForm, setSnapshotForm] = useState({
    name: '',
    description: ''
  })

  useEffect(() => {
    if (currentProject) {
      loadProjectData()
    }
  }, [currentProject])

  const loadProjectData = async () => {
    if (!currentProject) return

    try {
      setLoading(true)
      
      // 加载项目统计信息
      const statsResponse = await ProjectAPI.getProject(currentProject.id)
      if (statsResponse.success) {
        // 这里应该调用专门的统计API
        // const stats = await ProjectAPI.getProjectStatistics(currentProject.id)
        // setProjectStats(stats.data)
      }

      // 加载快照列表
      // const snapshotsResponse = await ProjectAPI.getProjectSnapshots(currentProject.id)
      // if (snapshotsResponse.success) {
      //   setSnapshots(snapshotsResponse.data)
      // }

      // 模拟数据
      setProjectStats({
        roads_count: 5,
        terrain_data_count: 2,
        uploads_count: 8,
        snapshots_count: 12,
        total_road_length: 2450.5,
        total_file_size: 156789123,
        project_age_days: 15,
        last_modified: new Date().toISOString()
      })

      setSnapshots([
        {
          id: 1,
          name: '初始设计',
          description: '项目初始道路设计方案',
          version: '1.0',
          created_at: '2024-01-20T10:00:00Z',
          is_auto_save: false,
          changes_summary: '创建了主干道和3条次干道'
        },
        {
          id: 2,
          name: '优化方案',
          description: '根据地形分析优化的道路方案',
          version: '1.1',
          created_at: '2024-01-22T14:30:00Z',
          is_auto_save: false,
          changes_summary: '调整了2号道路的路径，降低了坡度'
        },
        {
          id: 3,
          name: '自动保存',
          description: '系统自动保存',
          version: '1.2',
          created_at: '2024-01-25T09:15:00Z',
          is_auto_save: true,
          changes_summary: '添加了新的测量点'
        }
      ])

    } catch (error) {
      console.error('加载项目数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCreateSnapshot = async () => {
    if (!currentProject || !snapshotForm.name.trim()) return

    try {
      setLoading(true)
      
      // 调用API创建快照
      // const response = await ProjectAPI.createSnapshot(currentProject.id, snapshotForm)
      // if (response.success) {
      //   await loadProjectData()
      //   setShowCreateSnapshot(false)
      //   setSnapshotForm({ name: '', description: '' })
      // }

      // 模拟成功
      await new Promise(resolve => setTimeout(resolve, 1000))
      setShowCreateSnapshot(false)
      setSnapshotForm({ name: '', description: '' })
      await loadProjectData()

    } catch (error) {
      console.error('创建快照失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleRestoreSnapshot = async (snapshotId: number) => {
    if (!currentProject) return

    if (!confirm('确定要恢复到此快照吗？当前未保存的更改将丢失。')) {
      return
    }

    try {
      setLoading(true)
      
      // 调用API恢复快照
      // const response = await ProjectAPI.restoreSnapshot(snapshotId)
      // if (response.success) {
      //   // 刷新页面或重新加载项目数据
      //   window.location.reload()
      // }

      // 模拟成功
      await new Promise(resolve => setTimeout(resolve, 1500))
      alert('快照恢复成功！')

    } catch (error) {
      console.error('恢复快照失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleExportProject = async () => {
    if (!currentProject) return

    try {
      setLoading(true)
      
      // 调用API导出项目
      // const response = await ProjectAPI.exportProject(currentProject.id)
      // if (response.success) {
      //   // 下载文件
      //   const blob = new Blob([JSON.stringify(response.data, null, 2)], { type: 'application/json' })
      //   const url = URL.createObjectURL(blob)
      //   const a = document.createElement('a')
      //   a.href = url
      //   a.download = `${currentProject.name}_export_${new Date().toISOString().split('T')[0]}.json`
      //   a.click()
      //   URL.revokeObjectURL(url)
      // }

      // 模拟导出
      await new Promise(resolve => setTimeout(resolve, 1000))
      const exportData = {
        project: currentProject,
        exported_at: new Date().toISOString()
      }
      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${currentProject.name}_export.json`
      a.click()
      URL.revokeObjectURL(url)

    } catch (error) {
      console.error('导出项目失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatFileSize = (bytes: number): string => {
    const sizes = ['B', 'KB', 'MB', 'GB']
    if (bytes === 0) return '0 B'
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleString('zh-CN')
  }

  if (!currentProject) {
    return (
      <div className="bg-dark-800 border border-gray-700 rounded-lg p-6 text-center">
        <Database className="w-12 h-12 text-gray-500 mx-auto mb-3" />
        <p className="text-gray-400">请先选择一个项目</p>
      </div>
    )
  }

  return (
    <>
      <div className="space-y-6">
        {/* 项目统计 */}
        {projectStats && (
          <div className="bg-dark-800 border border-gray-700 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <Database className="w-5 h-5 mr-2 text-primary-400" />
              项目统计
            </h3>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-dark-700 rounded-lg p-3 text-center">
                <div className="text-2xl font-bold text-primary-400">{projectStats.roads_count}</div>
                <div className="text-xs text-gray-400">道路数量</div>
              </div>
              
              <div className="bg-dark-700 rounded-lg p-3 text-center">
                <div className="text-2xl font-bold text-green-400">
                  {(projectStats.total_road_length / 1000).toFixed(1)}
                </div>
                <div className="text-xs text-gray-400">总长度 (km)</div>
              </div>
              
              <div className="bg-dark-700 rounded-lg p-3 text-center">
                <div className="text-2xl font-bold text-blue-400">{projectStats.terrain_data_count}</div>
                <div className="text-xs text-gray-400">地形数据</div>
              </div>
              
              <div className="bg-dark-700 rounded-lg p-3 text-center">
                <div className="text-2xl font-bold text-yellow-400">
                  {formatFileSize(projectStats.total_file_size)}
                </div>
                <div className="text-xs text-gray-400">文件大小</div>
              </div>
            </div>
          </div>
        )}

        {/* 快照管理 */}
        <div className="bg-dark-800 border border-gray-700 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white flex items-center">
              <History className="w-5 h-5 mr-2 text-primary-400" />
              版本快照
            </h3>
            <Button
              variant="primary"
              size="sm"
              onClick={() => setShowCreateSnapshot(true)}
            >
              <Save className="w-4 h-4 mr-2" />
              创建快照
            </Button>
          </div>

          <div className="space-y-3">
            {snapshots.map((snapshot) => (
              <div
                key={snapshot.id}
                className="bg-dark-700 border border-gray-600 rounded-lg p-4 hover:border-gray-500 transition-colors"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <h4 className="font-medium text-white">{snapshot.name}</h4>
                      <span className="text-xs px-2 py-1 bg-primary-500/20 text-primary-400 rounded">
                        v{snapshot.version}
                      </span>
                      {snapshot.is_auto_save && (
                        <span className="text-xs px-2 py-1 bg-gray-600 text-gray-300 rounded">
                          自动
                        </span>
                      )}
                    </div>
                    
                    {snapshot.description && (
                      <p className="text-sm text-gray-400 mb-2">{snapshot.description}</p>
                    )}
                    
                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <span className="flex items-center">
                        <Clock className="w-3 h-3 mr-1" />
                        {formatDate(snapshot.created_at)}
                      </span>
                      {snapshot.changes_summary && (
                        <span>{snapshot.changes_summary}</span>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRestoreSnapshot(snapshot.id)}
                      loading={loading}
                    >
                      恢复
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {/* 删除快照 */}}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 导入导出 */}
        <div className="bg-dark-800 border border-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <FileText className="w-5 h-5 mr-2 text-primary-400" />
            数据管理
          </h3>
          
          <div className="flex space-x-3">
            <Button
              variant="secondary"
              onClick={handleExportProject}
              loading={loading}
            >
              <Download className="w-4 h-4 mr-2" />
              导出项目
            </Button>
            
            <Button
              variant="secondary"
              onClick={() => setShowImportExport(true)}
            >
              <Upload className="w-4 h-4 mr-2" />
              导入项目
            </Button>
          </div>
        </div>
      </div>

      {/* 创建快照模态框 */}
      <Modal
        isOpen={showCreateSnapshot}
        onClose={() => setShowCreateSnapshot(false)}
        title="创建项目快照"
      >
        <ModalBody>
          <div className="space-y-4">
            <Input
              label="快照名称"
              value={snapshotForm.name}
              onChange={(e) => setSnapshotForm({...snapshotForm, name: e.target.value})}
              placeholder="输入快照名称"
            />
            
            <Input
              label="描述 (可选)"
              value={snapshotForm.description}
              onChange={(e) => setSnapshotForm({...snapshotForm, description: e.target.value})}
              placeholder="描述此快照的主要变更"
            />
          </div>
        </ModalBody>

        <ModalFooter>
          <Button variant="ghost" onClick={() => setShowCreateSnapshot(false)}>
            取消
          </Button>
          <Button
            variant="primary"
            onClick={handleCreateSnapshot}
            loading={loading}
            disabled={!snapshotForm.name.trim()}
          >
            创建快照
          </Button>
        </ModalFooter>
      </Modal>
    </>
  )
}
