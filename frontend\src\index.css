@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式 */
@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-dark-900 text-gray-100 antialiased;
    overflow: hidden;
  }
  
  /* 滚动条样式 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-dark-800;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-gray-600 rounded-md;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-primary-500;
  }
}

/* 组件样式 */
@layer components {
  /* 按钮样式 */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-dark-900;
  }
  
  .btn-primary {
    @apply btn bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-gray-700 text-gray-100 hover:bg-gray-600 focus:ring-gray-500;
  }
  
  .btn-ghost {
    @apply btn bg-transparent text-gray-300 hover:bg-gray-800 hover:text-white focus:ring-gray-500;
  }
  
  .btn-danger {
    @apply btn bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }
  
  /* 输入框样式 */
  .input {
    @apply w-full px-3 py-2 text-sm bg-dark-800 border border-gray-600 rounded-lg placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200;
  }
  
  /* 卡片样式 */
  .card {
    @apply bg-dark-800 border border-gray-700 rounded-lg shadow-lg;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-700;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  /* 工具栏样式 */
  .toolbar {
    @apply flex items-center space-x-2 p-3 bg-dark-800/80 backdrop-blur-sm border border-gray-700 rounded-lg;
  }
  
  /* 侧边栏样式 */
  .sidebar {
    @apply w-80 h-full bg-dark-800 border-r border-gray-700 flex flex-col;
  }
  
  .sidebar-header {
    @apply px-6 py-4 border-b border-gray-700 flex items-center justify-between;
  }
  
  .sidebar-content {
    @apply flex-1 overflow-y-auto p-4;
  }
  
  /* 面板样式 */
  .panel {
    @apply bg-dark-800/95 backdrop-blur-sm border border-gray-700 rounded-lg shadow-xl;
  }
  
  .panel-header {
    @apply px-4 py-3 border-b border-gray-700 flex items-center justify-between;
  }
  
  .panel-content {
    @apply p-4;
  }
}

/* 工具类 */
@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent;
  }
  
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }
  
  .glass-dark {
    @apply bg-dark-900/80 backdrop-blur-md border border-gray-700/50;
  }
  
  /* 动画类 */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .animate-pulse-glow {
    animation: pulseGlow 2s infinite;
  }
}

/* 动画定义 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(245, 158, 11, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(245, 158, 11, 0.8);
  }
}

/* Cesium样式覆盖 */
.cesium-viewer {
  font-family: 'Inter', system-ui, sans-serif !important;
}

.cesium-viewer-toolbar {
  background: rgba(15, 23, 42, 0.9) !important;
  border: 1px solid rgba(55, 65, 81, 0.5) !important;
  border-radius: 8px !important;
  backdrop-filter: blur(10px) !important;
}

.cesium-button {
  background: rgba(55, 65, 81, 0.8) !important;
  border: 1px solid rgba(107, 114, 128, 0.3) !important;
  color: #f1f5f9 !important;
  border-radius: 6px !important;
}

.cesium-button:hover {
  background: rgba(245, 158, 11, 0.2) !important;
  border-color: #f59e0b !important;
}

.cesium-infoBox {
  background: rgba(15, 23, 42, 0.95) !important;
  border: 1px solid rgba(55, 65, 81, 0.5) !important;
  border-radius: 8px !important;
  backdrop-filter: blur(10px) !important;
}

.cesium-infoBox-title {
  background: rgba(245, 158, 11, 0.1) !important;
  color: #f59e0b !important;
  border-bottom: 1px solid rgba(245, 158, 11, 0.3) !important;
}
