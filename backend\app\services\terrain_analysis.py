"""
地形分析服务
"""

import numpy as np
import rasterio
from rasterio.features import shapes
from shapely.geometry import shape, Point
from typing import Dict, List, Tuple, Optional, Union
import math


class TerrainAnalysisService:
    """地形分析服务"""
    
    @staticmethod
    def load_dem_data(file_path: str) -> <PERSON><PERSON>[np.ndarray, Dict]:
        """加载DEM数据"""
        try:
            with rasterio.open(file_path) as dataset:
                elevation_data = dataset.read(1)
                metadata = {
                    'transform': dataset.transform,
                    'crs': dataset.crs,
                    'width': dataset.width,
                    'height': dataset.height,
                    'bounds': dataset.bounds,
                    'nodata': dataset.nodata
                }
                return elevation_data, metadata
        except Exception as e:
            raise ValueError(f"无法加载DEM数据: {str(e)}")
    
    @staticmethod
    def calculate_slope(elevation_data: np.ndarray, cell_size: float = 1.0) -> np.ndarray:
        """计算坡度"""
        # 使用Sobel算子计算梯度
        grad_x = np.gradient(elevation_data, axis=1) / cell_size
        grad_y = np.gradient(elevation_data, axis=0) / cell_size
        
        # 计算坡度（度）
        slope_radians = np.arctan(np.sqrt(grad_x**2 + grad_y**2))
        slope_degrees = np.degrees(slope_radians)
        
        return slope_degrees
    
    @staticmethod
    def calculate_aspect(elevation_data: np.ndarray, cell_size: float = 1.0) -> np.ndarray:
        """计算坡向"""
        # 计算梯度
        grad_x = np.gradient(elevation_data, axis=1) / cell_size
        grad_y = np.gradient(elevation_data, axis=0) / cell_size
        
        # 计算坡向（度，从北开始顺时针）
        aspect_radians = np.arctan2(-grad_x, grad_y)
        aspect_degrees = np.degrees(aspect_radians)
        
        # 转换为0-360度
        aspect_degrees = np.where(aspect_degrees < 0, aspect_degrees + 360, aspect_degrees)
        
        return aspect_degrees
    
    @staticmethod
    def calculate_hillshade(elevation_data: np.ndarray, azimuth: float = 315.0, 
                          altitude: float = 45.0, cell_size: float = 1.0) -> np.ndarray:
        """计算山体阴影"""
        # 转换角度为弧度
        azimuth_rad = np.radians(azimuth)
        altitude_rad = np.radians(altitude)
        
        # 计算坡度和坡向
        slope = TerrainAnalysisService.calculate_slope(elevation_data, cell_size)
        aspect = TerrainAnalysisService.calculate_aspect(elevation_data, cell_size)
        
        slope_rad = np.radians(slope)
        aspect_rad = np.radians(aspect)
        
        # 计算山体阴影
        hillshade = (
            np.cos(altitude_rad) * np.cos(slope_rad) +
            np.sin(altitude_rad) * np.sin(slope_rad) * 
            np.cos(azimuth_rad - aspect_rad)
        )
        
        # 标准化到0-255
        hillshade = np.clip(hillshade * 255, 0, 255).astype(np.uint8)
        
        return hillshade
    
    @staticmethod
    def analyze_terrain_statistics(elevation_data: np.ndarray, 
                                 slope_data: Optional[np.ndarray] = None) -> Dict:
        """分析地形统计信息"""
        # 过滤无效值
        valid_elevation = elevation_data[~np.isnan(elevation_data)]
        
        if len(valid_elevation) == 0:
            return {"error": "没有有效的高程数据"}
        
        stats = {
            "elevation": {
                "min": float(np.min(valid_elevation)),
                "max": float(np.max(valid_elevation)),
                "mean": float(np.mean(valid_elevation)),
                "std": float(np.std(valid_elevation)),
                "range": float(np.max(valid_elevation) - np.min(valid_elevation))
            }
        }
        
        if slope_data is not None:
            valid_slope = slope_data[~np.isnan(slope_data)]
            if len(valid_slope) > 0:
                stats["slope"] = {
                    "min": float(np.min(valid_slope)),
                    "max": float(np.max(valid_slope)),
                    "mean": float(np.mean(valid_slope)),
                    "std": float(np.std(valid_slope))
                }
        
        return stats
    
    @staticmethod
    def classify_slope(slope_data: np.ndarray) -> Dict:
        """坡度分级"""
        # 定义坡度等级
        slope_classes = {
            "平地": (0, 5),
            "缓坡": (5, 15),
            "斜坡": (15, 25),
            "陡坡": (25, 35),
            "急坡": (35, float('inf'))
        }
        
        total_pixels = np.sum(~np.isnan(slope_data))
        if total_pixels == 0:
            return {"error": "没有有效的坡度数据"}
        
        classification = {}
        for class_name, (min_slope, max_slope) in slope_classes.items():
            if max_slope == float('inf'):
                mask = slope_data >= min_slope
            else:
                mask = (slope_data >= min_slope) & (slope_data < max_slope)
            
            count = np.sum(mask & ~np.isnan(slope_data))
            percentage = (count / total_pixels) * 100
            
            classification[class_name] = {
                "count": int(count),
                "percentage": float(percentage)
            }
        
        return classification
    
    @staticmethod
    def classify_aspect(aspect_data: np.ndarray) -> Dict:
        """坡向分级"""
        # 定义坡向等级
        aspect_classes = {
            "北": (337.5, 22.5),
            "东北": (22.5, 67.5),
            "东": (67.5, 112.5),
            "东南": (112.5, 157.5),
            "南": (157.5, 202.5),
            "西南": (202.5, 247.5),
            "西": (247.5, 292.5),
            "西北": (292.5, 337.5)
        }
        
        total_pixels = np.sum(~np.isnan(aspect_data))
        if total_pixels == 0:
            return {"error": "没有有效的坡向数据"}
        
        classification = {}
        for class_name, (min_aspect, max_aspect) in aspect_classes.items():
            if class_name == "北":  # 北向需要特殊处理（跨越0度）
                mask = (aspect_data >= min_aspect) | (aspect_data < max_aspect)
            else:
                mask = (aspect_data >= min_aspect) & (aspect_data < max_aspect)
            
            count = np.sum(mask & ~np.isnan(aspect_data))
            percentage = (count / total_pixels) * 100
            
            classification[class_name] = {
                "count": int(count),
                "percentage": float(percentage)
            }
        
        return classification
    
    @staticmethod
    def calculate_viewshed(elevation_data: np.ndarray, observer_x: int, observer_y: int,
                          observer_height: float = 1.7, target_height: float = 0.0,
                          max_distance: float = 1000.0, cell_size: float = 1.0) -> np.ndarray:
        """计算通视分析"""
        rows, cols = elevation_data.shape
        viewshed = np.zeros((rows, cols), dtype=bool)
        
        observer_elevation = elevation_data[observer_y, observer_x] + observer_height
        
        # 遍历所有像元
        for y in range(rows):
            for x in range(cols):
                # 计算距离
                distance = math.sqrt((x - observer_x)**2 + (y - observer_y)**2) * cell_size
                
                if distance > max_distance or distance == 0:
                    continue
                
                target_elevation = elevation_data[y, x] + target_height
                
                # 计算视线角度
                angle_to_target = math.atan2(target_elevation - observer_elevation, distance)
                
                # 检查视线是否被遮挡
                visible = True
                steps = int(distance / cell_size)
                
                for step in range(1, steps):
                    t = step / steps
                    check_x = int(observer_x + t * (x - observer_x))
                    check_y = int(observer_y + t * (y - observer_y))
                    
                    if 0 <= check_x < cols and 0 <= check_y < rows:
                        check_distance = step * cell_size
                        check_elevation = elevation_data[check_y, check_x]
                        required_elevation = observer_elevation + check_distance * math.tan(angle_to_target)
                        
                        if check_elevation > required_elevation:
                            visible = False
                            break
                
                viewshed[y, x] = visible
        
        return viewshed
    
    @staticmethod
    def extract_contours(elevation_data: np.ndarray, interval: float = 10.0,
                        transform: Optional[object] = None) -> List[Dict]:
        """提取等高线"""
        try:
            # 生成等高线级别
            min_elev = np.nanmin(elevation_data)
            max_elev = np.nanmax(elevation_data)
            
            levels = np.arange(
                math.floor(min_elev / interval) * interval,
                math.ceil(max_elev / interval) * interval + interval,
                interval
            )
            
            contours = []
            
            for level in levels:
                # 创建二值掩码
                mask = (elevation_data >= level - interval/2) & (elevation_data < level + interval/2)
                
                # 提取形状
                for geom, value in shapes(mask.astype(np.uint8), transform=transform):
                    if value == 1:  # 只处理有效区域
                        contours.append({
                            "elevation": float(level),
                            "geometry": geom
                        })
            
            return contours
            
        except Exception as e:
            return [{"error": f"等高线提取失败: {str(e)}"}]
    
    @staticmethod
    def calculate_cut_fill_volume(elevation_data: np.ndarray, design_elevation: np.ndarray,
                                cell_size: float = 1.0) -> Dict:
        """计算挖填方量"""
        if elevation_data.shape != design_elevation.shape:
            return {"error": "地形数据和设计高程数据尺寸不匹配"}
        
        # 计算高程差
        elevation_diff = design_elevation - elevation_data
        
        # 分离挖方和填方
        cut_mask = elevation_diff < 0  # 挖方（设计高程低于地面）
        fill_mask = elevation_diff > 0  # 填方（设计高程高于地面）
        
        # 计算体积
        cell_area = cell_size * cell_size
        cut_volume = np.sum(np.abs(elevation_diff[cut_mask])) * cell_area
        fill_volume = np.sum(elevation_diff[fill_mask]) * cell_area
        
        return {
            "cut_volume": float(cut_volume),
            "fill_volume": float(fill_volume),
            "net_volume": float(fill_volume - cut_volume),
            "total_volume": float(cut_volume + fill_volume)
        }
