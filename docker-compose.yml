version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15
    container_name: openpit_postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DB: openpit_road_design
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - openpit_network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: openpit_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - openpit_network

  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: openpit_backend
    environment:
      - ENVIRONMENT=production
      - DEBUG=false
      - POSTGRES_SERVER=postgres
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=openpit_road_design
      - REDIS_URL=redis://redis:6379
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - uploads_data:/app/uploads
    depends_on:
      - postgres
      - redis
    networks:
      - openpit_network
    restart: unless-stopped

  # 前端应用
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: openpit_frontend
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - openpit_network
    restart: unless-stopped

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: openpit_nginx
    ports:
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - openpit_network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  uploads_data:

networks:
  openpit_network:
    driver: bridge
