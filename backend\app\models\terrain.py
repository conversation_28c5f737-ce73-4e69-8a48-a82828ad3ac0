"""
地形数据模型
"""

from sqlalchemy import Column, String, Float, Integer, ForeignKey, Enum, JSON, LargeBinary
from sqlalchemy.orm import relationship
import enum

from .base import BaseModel


class TerrainDataType(str, enum.Enum):
    """地形数据类型枚举"""
    DEM = "dem"              # 数字高程模型
    CONTOUR = "contour"      # 等高线
    POINT_CLOUD = "point-cloud"  # 点云数据
    MESH = "mesh"            # 三维网格


class TerrainFormat(str, enum.Enum):
    """地形数据格式枚举"""
    GEOTIFF = "geotiff"      # GeoTIFF
    ASCII_GRID = "asc"       # ASCII Grid
    XYZ = "xyz"              # XYZ点文件
    LAS = "las"              # LAS点云
    LAZ = "laz"              # LAZ压缩点云
    SHAPEFILE = "shp"        # Shapefile
    KML = "kml"              # KML
    GEOJSON = "geojson"      # GeoJSON


class TerrainData(BaseModel):
    """地形数据模型"""
    __tablename__ = "terrain_data"
    
    name = Column(String(200), nullable=False, index=True)
    description = Column(String(500), nullable=True)
    type = Column(Enum(TerrainDataType), nullable=False)
    format = Column(Enum(TerrainFormat), nullable=False)
    
    # 文件信息
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer, nullable=False)  # 文件大小(字节)
    original_filename = Column(String(255), nullable=False)
    
    # 空间信息
    resolution = Column(Float, nullable=True)  # 分辨率(米)
    extent = Column(JSON, nullable=True)  # 边界范围 {minX, maxX, minY, maxY, minZ, maxZ}
    coordinate_system = Column(String(100), nullable=True)  # 坐标系统
    
    # 统计信息
    min_elevation = Column(Float, nullable=True)
    max_elevation = Column(Float, nullable=True)
    mean_elevation = Column(Float, nullable=True)
    std_elevation = Column(Float, nullable=True)
    
    # 处理状态
    is_processed = Column(String(20), default="pending", nullable=False)  # pending, processing, completed, failed
    processing_log = Column(String(1000), nullable=True)
    
    # 外键
    project_id = Column(Integer, ForeignKey('projects.id'), nullable=False)
    uploaded_by = Column(Integer, ForeignKey('users.id'), nullable=False)
    
    # 关联关系
    project = relationship("Project", back_populates="terrain_data")
    uploader = relationship("User")
    
    @property
    def area_km2(self) -> float:
        """计算覆盖面积(平方公里)"""
        if not self.extent:
            return 0.0
        
        extent = self.extent
        width = extent.get('maxX', 0) - extent.get('minX', 0)
        height = extent.get('maxY', 0) - extent.get('minY', 0)
        
        # 简化计算，实际应考虑坐标系统
        return (width * height) / 1000000  # 转换为平方公里
    
    @property
    def elevation_range(self) -> float:
        """高程范围"""
        if self.min_elevation is None or self.max_elevation is None:
            return 0.0
        return self.max_elevation - self.min_elevation


class TerrainAnalysisResult(BaseModel):
    """地形分析结果模型"""
    __tablename__ = "terrain_analysis_results"
    
    name = Column(String(200), nullable=False)
    analysis_type = Column(String(50), nullable=False)  # slope, aspect, hillshade, viewshed等
    
    # 分析参数
    parameters = Column(JSON, nullable=True)
    
    # 结果数据
    result_data = Column(JSON, nullable=True)  # 存储分析结果的统计信息
    result_file_path = Column(String(500), nullable=True)  # 结果文件路径
    
    # 外键
    terrain_data_id = Column(Integer, ForeignKey('terrain_data.id'), nullable=False)
    project_id = Column(Integer, ForeignKey('projects.id'), nullable=False)
    
    # 关联关系
    terrain_data = relationship("TerrainData")
    project = relationship("Project")


class ProjectSnapshot(BaseModel):
    """项目快照模型 - 用于版本控制"""
    __tablename__ = "project_snapshots"
    
    name = Column(String(200), nullable=False)
    description = Column(String(500), nullable=True)
    version = Column(String(50), nullable=False)
    
    # 快照数据
    project_data = Column(JSON, nullable=False)  # 项目完整数据的JSON序列化
    roads_data = Column(JSON, nullable=True)     # 道路数据
    terrain_data_refs = Column(JSON, nullable=True)  # 地形数据引用
    
    # 变更信息
    changes_summary = Column(String(1000), nullable=True)
    is_auto_save = Column(String(10), default="false", nullable=False)
    
    # 外键
    project_id = Column(Integer, ForeignKey('projects.id'), nullable=False)
    created_by = Column(Integer, ForeignKey('users.id'), nullable=False)
    
    # 关联关系
    project = relationship("Project")
    creator = relationship("User")


class FileUpload(BaseModel):
    """文件上传记录模型"""
    __tablename__ = "file_uploads"
    
    filename = Column(String(255), nullable=False)
    original_name = Column(String(255), nullable=False)
    mime_type = Column(String(100), nullable=False)
    file_size = Column(Integer, nullable=False)
    file_path = Column(String(500), nullable=False)
    
    # 文件类型和用途
    file_type = Column(String(50), nullable=False)  # terrain, image, document, other
    purpose = Column(String(100), nullable=True)    # 文件用途描述
    
    # 处理状态
    status = Column(String(20), default="uploaded", nullable=False)  # uploaded, processing, processed, failed
    processing_result = Column(JSON, nullable=True)
    
    # 外键
    project_id = Column(Integer, ForeignKey('projects.id'), nullable=True)
    uploaded_by = Column(Integer, ForeignKey('users.id'), nullable=False)
    
    # 关联关系
    project = relationship("Project", back_populates="uploads")
    uploaded_by_user = relationship("User", back_populates="uploads")
    
    @property
    def file_size_mb(self) -> float:
        """文件大小(MB)"""
        return self.file_size / (1024 * 1024)
    
    @property
    def is_image(self) -> bool:
        """是否为图片文件"""
        image_types = ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/tiff']
        return self.mime_type in image_types
    
    @property
    def is_terrain_data(self) -> bool:
        """是否为地形数据文件"""
        terrain_extensions = ['.tif', '.tiff', '.asc', '.xyz', '.las', '.laz', '.dem']
        return any(self.filename.lower().endswith(ext) for ext in terrain_extensions)
