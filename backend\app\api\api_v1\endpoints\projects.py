"""
项目管理相关API端点
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime

router = APIRouter()


class ProjectCreate(BaseModel):
    """创建项目请求模型"""
    name: str
    description: Optional[str] = None
    location: str
    team_members: List[str] = []


class ProjectUpdate(BaseModel):
    """更新项目请求模型"""
    name: Optional[str] = None
    description: Optional[str] = None
    location: Optional[str] = None
    status: Optional[str] = None
    team_members: Optional[List[str]] = None


class ProjectResponse(BaseModel):
    """项目响应模型"""
    id: int
    name: str
    description: Optional[str]
    status: str
    progress: float
    location: str
    team_members: List[str]
    roads_count: int
    total_length: float
    created_at: datetime
    updated_at: datetime


class ProjectListResponse(BaseModel):
    """项目列表响应模型"""
    projects: List[ProjectResponse]
    total: int
    page: int
    page_size: int


# 模拟数据
MOCK_PROJECTS = [
    {
        "id": 1,
        "name": "矿山A区主干道设计",
        "description": "连接矿区主要作业面的主干道路设计项目",
        "status": "进行中",
        "progress": 75.0,
        "location": "内蒙古包头市",
        "team_members": ["张工程师", "李工程师", "王工程师"],
        "roads_count": 8,
        "total_length": 12.5,
        "created_at": "2024-01-15T08:00:00Z",
        "updated_at": "2024-01-25T10:30:00Z"
    },
    {
        "id": 2,
        "name": "B区运输道路优化",
        "description": "优化现有运输道路，提高运输效率",
        "status": "审核中",
        "progress": 90.0,
        "location": "山西大同市",
        "team_members": ["陈工程师", "刘工程师"],
        "roads_count": 5,
        "total_length": 8.2,
        "created_at": "2024-01-10T09:00:00Z",
        "updated_at": "2024-01-24T15:20:00Z"
    },
    {
        "id": 3,
        "name": "C区新建道路规划",
        "description": "新开采区域的道路网络规划设计",
        "status": "设计中",
        "progress": 45.0,
        "location": "陕西榆林市",
        "team_members": ["赵工程师", "孙工程师", "周工程师"],
        "roads_count": 12,
        "total_length": 18.7,
        "created_at": "2024-01-20T10:00:00Z",
        "updated_at": "2024-01-22T14:45:00Z"
    }
]


@router.get("/", response_model=ProjectListResponse)
async def get_projects(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    search: Optional[str] = Query(None),
    status: Optional[str] = Query(None)
):
    """
    获取项目列表
    """
    # TODO: 实现真实的数据库查询
    # 1. 根据搜索条件过滤项目
    # 2. 实现分页
    # 3. 返回项目列表
    
    # 模拟过滤和分页
    filtered_projects = MOCK_PROJECTS
    
    if search:
        filtered_projects = [
            p for p in filtered_projects 
            if search.lower() in p["name"].lower() or search.lower() in p["location"].lower()
        ]
    
    if status:
        filtered_projects = [p for p in filtered_projects if p["status"] == status]
    
    total = len(filtered_projects)
    start = (page - 1) * page_size
    end = start + page_size
    projects = filtered_projects[start:end]
    
    return {
        "projects": projects,
        "total": total,
        "page": page,
        "page_size": page_size
    }


@router.get("/{project_id}", response_model=ProjectResponse)
async def get_project(project_id: int):
    """
    获取单个项目详情
    """
    # TODO: 从数据库获取项目详情
    project = next((p for p in MOCK_PROJECTS if p["id"] == project_id), None)
    
    if not project:
        raise HTTPException(status_code=404, detail="项目不存在")
    
    return project


@router.post("/", response_model=ProjectResponse)
async def create_project(project: ProjectCreate):
    """
    创建新项目
    """
    # TODO: 实现项目创建逻辑
    # 1. 验证项目数据
    # 2. 保存到数据库
    # 3. 返回创建的项目信息
    
    new_project = {
        "id": len(MOCK_PROJECTS) + 1,
        "name": project.name,
        "description": project.description,
        "status": "设计中",
        "progress": 0.0,
        "location": project.location,
        "team_members": project.team_members,
        "roads_count": 0,
        "total_length": 0.0,
        "created_at": datetime.now().isoformat() + "Z",
        "updated_at": datetime.now().isoformat() + "Z"
    }
    
    MOCK_PROJECTS.append(new_project)
    return new_project


@router.put("/{project_id}", response_model=ProjectResponse)
async def update_project(project_id: int, project_update: ProjectUpdate):
    """
    更新项目信息
    """
    # TODO: 实现项目更新逻辑
    project = next((p for p in MOCK_PROJECTS if p["id"] == project_id), None)
    
    if not project:
        raise HTTPException(status_code=404, detail="项目不存在")
    
    # 更新字段
    if project_update.name is not None:
        project["name"] = project_update.name
    if project_update.description is not None:
        project["description"] = project_update.description
    if project_update.location is not None:
        project["location"] = project_update.location
    if project_update.status is not None:
        project["status"] = project_update.status
    if project_update.team_members is not None:
        project["team_members"] = project_update.team_members
    
    project["updated_at"] = datetime.now().isoformat() + "Z"
    
    return project


@router.delete("/{project_id}")
async def delete_project(project_id: int):
    """
    删除项目
    """
    # TODO: 实现项目删除逻辑
    # 1. 检查项目是否存在
    # 2. 检查是否有关联数据
    # 3. 删除项目及相关数据
    
    global MOCK_PROJECTS
    project = next((p for p in MOCK_PROJECTS if p["id"] == project_id), None)
    
    if not project:
        raise HTTPException(status_code=404, detail="项目不存在")
    
    MOCK_PROJECTS = [p for p in MOCK_PROJECTS if p["id"] != project_id]
    
    return {"message": "项目删除成功"}


@router.get("/{project_id}/statistics")
async def get_project_statistics(project_id: int):
    """
    获取项目统计信息
    """
    # TODO: 实现项目统计逻辑
    project = next((p for p in MOCK_PROJECTS if p["id"] == project_id), None)
    
    if not project:
        raise HTTPException(status_code=404, detail="项目不存在")
    
    return {
        "roads_count": project["roads_count"],
        "total_length": project["total_length"],
        "progress": project["progress"],
        "team_size": len(project["team_members"]),
        "estimated_cost": 1250000.0,  # 模拟数据
        "completion_date": "2024-03-15",  # 模拟数据
        "risk_level": "低"  # 模拟数据
    }
