"""
道路设计相关API端点
"""

from fastapi import APIRouter, HTTPException, Depends, Query, Body
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime

from app.services.road_design import RoadDesignService, DesignConstraints, Point3D
from app.services.terrain_analysis import TerrainAnalysisService
from app.models.road import Road, RoadType, SurfaceType

router = APIRouter()


class Coordinate(BaseModel):
    """坐标点模型"""
    longitude: float
    latitude: float
    elevation: Optional[float] = None


class RoadCreate(BaseModel):
    """创建道路请求模型"""
    name: str
    type: str = Field(..., description="道路类型: main, secondary, access, haul")
    width: float = Field(..., description="道路宽度(m)")
    max_grade: float = Field(..., description="最大坡度(%)")
    min_radius: float = Field(..., description="最小转弯半径(m)")
    surface_type: str = Field(..., description="路面类型: asphalt, concrete, gravel, dirt")
    coordinates: List[Coordinate] = Field(..., description="道路坐标点列表")
    project_id: int = Field(..., description="所属项目ID")


class RoadUpdate(BaseModel):
    """更新道路请求模型"""
    name: Optional[str] = None
    type: Optional[str] = None
    width: Optional[float] = None
    max_grade: Optional[float] = None
    min_radius: Optional[float] = None
    surface_type: Optional[str] = None
    coordinates: Optional[List[Coordinate]] = None
    status: Optional[str] = None


class RoadResponse(BaseModel):
    """道路响应模型"""
    id: int
    project_id: int
    name: str
    type: str
    width: float
    length: float
    max_grade: float
    min_radius: float
    surface_type: str
    coordinates: List[Coordinate]
    status: str
    created_at: str
    updated_at: str


class RoadAnalysisParams(BaseModel):
    """道路分析参数"""
    constraints: Optional[Dict[str, float]] = None
    include_earthwork: bool = True
    include_cost: bool = True


@router.get("/", response_model=List[RoadResponse])
async def get_roads(
    project_id: Optional[int] = Query(None, description="项目ID筛选"),
    type: Optional[str] = Query(None, description="道路类型筛选"),
    status: Optional[str] = Query(None, description="状态筛选")
):
    """
    获取道路列表，支持按项目、类型和状态筛选
    """
    # TODO: 实现数据库查询
    # 示例数据
    roads = [
        {
            "id": 1,
            "project_id": 1,
            "name": "主入场道路",
            "type": "main",
            "width": 8.0,
            "length": 1250.5,
            "max_grade": 8.0,
            "min_radius": 25.0,
            "surface_type": "asphalt",
            "coordinates": [
                {"longitude": 116.397, "latitude": 39.908, "elevation": 120.5},
                {"longitude": 116.398, "latitude": 39.909, "elevation": 125.2},
                {"longitude": 116.399, "latitude": 39.910, "elevation": 130.8}
            ],
            "status": "designed",
            "created_at": "2024-01-25T10:00:00Z",
            "updated_at": "2024-01-25T10:00:00Z"
        },
        {
            "id": 2,
            "project_id": 1,
            "name": "采区连接道路",
            "type": "haul",
            "width": 12.0,
            "length": 850.3,
            "max_grade": 6.5,
            "min_radius": 30.0,
            "surface_type": "gravel",
            "coordinates": [
                {"longitude": 116.397, "latitude": 39.905, "elevation": 118.2},
                {"longitude": 116.398, "latitude": 39.906, "elevation": 122.5},
                {"longitude": 116.399, "latitude": 39.907, "elevation": 126.7}
            ],
            "status": "draft",
            "created_at": "2024-01-26T10:00:00Z",
            "updated_at": "2024-01-26T10:00:00Z"
        }
    ]

    # 应用过滤
    filtered_roads = roads
    if project_id is not None:
        filtered_roads = [road for road in filtered_roads if road["project_id"] == project_id]
    if type is not None:
        filtered_roads = [road for road in filtered_roads if road["type"] == type]
    if status is not None:
        filtered_roads = [road for road in filtered_roads if road["status"] == status]

    return filtered_roads


@router.post("/", response_model=RoadResponse)
async def create_road(road: RoadCreate):
    """
    创建新道路

    - 验证坐标数据
    - 计算道路长度
    - 存储到数据库
    """
    # 验证坐标（至少需要两个点）
    if len(road.coordinates) < 2:
        raise HTTPException(status_code=400, detail="道路至少需要两个坐标点")

    # 使用道路设计服务计算几何特性
    coordinates_dict = [coord.dict() for coord in road.coordinates]
    geometry = RoadDesignService.analyze_road_geometry(coordinates_dict)

    if "error" in geometry:
        raise HTTPException(status_code=400, detail=geometry["error"])

    # 创建新道路（实际应用中会存入数据库）
    now = datetime.now().isoformat()
    new_road = {
        "id": 3,  # 实际应用中使用数据库自增ID
        "project_id": road.project_id,
        "name": road.name,
        "type": road.type,
        "width": road.width,
        "length": geometry["total_length"],
        "max_grade": road.max_grade,
        "min_radius": road.min_radius,
        "surface_type": road.surface_type,
        "coordinates": road.coordinates,
        "status": "draft",
        "created_at": now,
        "updated_at": now
    }

    return new_road


@router.get("/{road_id}", response_model=RoadResponse)
async def get_road(road_id: int):
    """
    获取单个道路详情
    """
    # TODO: 实现数据库查询
    road = {
        "id": road_id,
        "project_id": 1,
        "name": "主入场道路",
        "type": "main",
        "width": 8.0,
        "length": 1250.5,
        "max_grade": 8.0,
        "min_radius": 25.0,
        "surface_type": "asphalt",
        "coordinates": [
            {"longitude": 116.397, "latitude": 39.908, "elevation": 120.5},
            {"longitude": 116.398, "latitude": 39.909, "elevation": 125.2},
            {"longitude": 116.399, "latitude": 39.910, "elevation": 130.8}
        ],
        "status": "designed",
        "created_at": "2024-01-25T10:00:00Z",
        "updated_at": "2024-01-25T10:00:00Z"
    }

    return road


@router.put("/{road_id}", response_model=RoadResponse)
async def update_road(road_id: int, road_update: RoadUpdate):
    """
    更新道路信息
    """
    # TODO: 实现数据库更新
    # 1. 获取现有道路
    existing_road = {
        "id": road_id,
        "project_id": 1,
        "name": "主入场道路",
        "type": "main",
        "width": 8.0,
        "length": 1250.5,
        "max_grade": 8.0,
        "min_radius": 25.0,
        "surface_type": "asphalt",
        "coordinates": [
            {"longitude": 116.397, "latitude": 39.908, "elevation": 120.5},
            {"longitude": 116.398, "latitude": 39.909, "elevation": 125.2},
            {"longitude": 116.399, "latitude": 39.910, "elevation": 130.8}
        ],
        "status": "designed",
        "created_at": "2024-01-25T10:00:00Z",
        "updated_at": "2024-01-25T10:00:00Z"
    }

    # 2. 应用更新
    update_data = road_update.dict(exclude_unset=True)
    for key, value in update_data.items():
        if value is not None:
            existing_road[key] = value

    # 3. 如果坐标变更，重新计算长度
    if "coordinates" in update_data and existing_road["coordinates"]:
        coordinates_dict = [coord.dict() for coord in existing_road["coordinates"]]
        geometry = RoadDesignService.analyze_road_geometry(coordinates_dict)
        existing_road["length"] = geometry["total_length"]

    existing_road["updated_at"] = datetime.now().isoformat()

    return existing_road


@router.delete("/{road_id}")
async def delete_road(road_id: int):
    """
    删除道路
    """
    # TODO: 实现数据库删除
    return {"message": f"道路 {road_id} 已删除"}


@router.post("/{road_id}/analyze")
async def analyze_road(road_id: int, params: RoadAnalysisParams = Body(None)):
    """
    分析道路设计，评估坡度、转弯半径等参数，计算土方量和成本
    """
    # TODO: 获取道路数据
    road_data = {
        "id": road_id,
        "project_id": 1,
        "name": "主入场道路",
        "type": "main",
        "width": 8.0,
        "length": 1250.5,
        "max_grade": 8.0,
        "min_radius": 25.0,
        "surface_type": "asphalt",
        "coordinates": [
            {"longitude": 116.397, "latitude": 39.908, "elevation": 120.5},
            {"longitude": 116.398, "latitude": 39.909, "elevation": 125.2},
            {"longitude": 116.399, "latitude": 39.910, "elevation": 130.8}
        ]
    }

    # 设置分析约束条件
    constraints = DesignConstraints()
    if params and params.constraints:
        for key, value in params.constraints.items():
            if hasattr(constraints, key):
                setattr(constraints, key, value)

    # 进行几何分析
    coordinates = road_data["coordinates"]
    geometry = RoadDesignService.analyze_road_geometry(coordinates)
    validation = RoadDesignService.validate_design(coordinates, constraints)

    result = {
        "road_id": road_id,
        "name": road_data["name"],
        "total_length": geometry["total_length"],
        "segment_count": geometry["segment_count"],
        "elevation_change": geometry["elevation_change"],
        "grades": geometry["grades"],
        "radii": geometry["radii"],
        "is_valid": validation["valid"],
        "violations": validation["violations"],
        "warnings": validation["warnings"]
    }

    # 计算土方量
    if params and params.include_earthwork:
        earthwork = RoadDesignService.calculate_earthwork_volume(
            coordinates, road_width=road_data["width"]
        )
        result["earthwork"] = earthwork

    # 估算成本
    if params and params.include_cost:
        # 构建简化的Road对象用于成本估算
        simple_road = Road(
            id=road_data["id"],
            project_id=road_data["project_id"],
            name=road_data["name"],
            type=RoadType.MAIN,  # 简化处理
            width=road_data["width"],
            length=geometry["total_length"],
            max_grade=road_data["max_grade"],
            min_radius=road_data["min_radius"],
            surface_type=SurfaceType.ASPHALT,  # 简化处理
            coordinates=[]
        )

        if "earthwork" not in result:
            earthwork = RoadDesignService.calculate_earthwork_volume(
                coordinates, road_width=road_data["width"]
            )
        else:
            earthwork = result["earthwork"]

        cost = RoadDesignService.estimate_construction_cost(simple_road, earthwork)
        result["cost_estimation"] = cost

    return result


@router.post("/optimize")
async def optimize_road(start_point: Coordinate, end_point: Coordinate, constraints: Dict = Body(...)):
    """
    优化两点间的道路路径
    """
    # 转换为Point3D对象
    start = Point3D(
        x=start_point.longitude,
        y=start_point.latitude,
        z=start_point.elevation if start_point.elevation else 0
    )

    end = Point3D(
        x=end_point.longitude,
        y=end_point.latitude,
        z=end_point.elevation if end_point.elevation else 0
    )

    # 创建约束条件
    design_constraints = DesignConstraints(
        max_grade=constraints.get("max_grade", 8.0),
        min_radius=constraints.get("min_radius", 25.0),
        road_width=constraints.get("road_width", 6.0),
        max_speed=constraints.get("max_speed", 30.0)
    )

    # 优化路径
    optimized_points = RoadDesignService.optimize_road_path(start, end, None, design_constraints)

    # 转换回Coordinate对象
    optimized_coordinates = [
        Coordinate(longitude=p.x, latitude=p.y, elevation=p.z) 
        for p in optimized_points
    ]

    return {
        "optimized_path": optimized_coordinates,
        "point_count": len(optimized_coordinates),
        "constraints_used": {
            "max_grade": design_constraints.max_grade,
            "min_radius": design_constraints.min_radius,
            "road_width": design_constraints.road_width
        }
    }

