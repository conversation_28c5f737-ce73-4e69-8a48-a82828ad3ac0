import React from 'react'
import {
  Upload,
  Download,
  Layers,
  BarChart3,
  Mountain,
  Ruler,
  Eye,
  EyeOff
} from 'lucide-react'

const TerrainViewer: React.FC = () => {
  const [selectedLayer, setSelectedLayer] = React.useState('elevation')
  
  const terrainLayers = [
    { id: 'elevation', name: '高程数据', type: 'DEM', visible: true, opacity: 100 },
    { id: 'slope', name: '坡度分析', type: '分析', visible: false, opacity: 80 },
    { id: 'aspect', name: '坡向分析', type: '分析', visible: false, opacity: 80 },
    { id: 'contour', name: '等高线', type: '矢量', visible: true, opacity: 70 },
    { id: 'drainage', name: '水系分析', type: '分析', visible: false, opacity: 60 }
  ]

  const analysisTools = [
    { id: 'profile', name: '剖面分析', icon: BarChart3 },
    { id: 'volume', name: '土方计算', icon: Mountain },
    { id: 'visibility', name: '通视分析', icon: Eye },
    { id: 'watershed', name: '流域分析', icon: Layers }
  ]

  return (
    <div className="h-full flex">
      {/* 左侧控制面板 */}
      <div className="w-80 bg-dark-800 border-r border-gray-700 flex flex-col">
        {/* 数据管理 */}
        <div className="p-4 border-b border-gray-700">
          <h3 className="text-lg font-semibold text-white mb-4">地形数据</h3>
          <div className="space-y-3">
            <button className="btn-primary w-full">
              <Upload className="w-4 h-4 mr-2" />
              导入地形数据
            </button>
            <button className="btn-secondary w-full">
              <Download className="w-4 h-4 mr-2" />
              导出分析结果
            </button>
          </div>
        </div>

        {/* 图层控制 */}
        <div className="p-4 border-b border-gray-700">
          <h4 className="text-md font-medium text-white mb-3">图层管理</h4>
          <div className="space-y-3">
            {terrainLayers.map((layer) => (
              <div key={layer.id} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => {/* 切换图层可见性 */}}
                      className="text-gray-400 hover:text-white"
                    >
                      {layer.visible ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
                    </button>
                    <span className="text-sm text-gray-300">{layer.name}</span>
                  </div>
                  <span className="text-xs text-gray-500 px-2 py-1 bg-dark-700 rounded">
                    {layer.type}
                  </span>
                </div>
                
                {layer.visible && (
                  <div className="ml-6">
                    <div className="flex items-center space-x-2">
                      <span className="text-xs text-gray-400">透明度:</span>
                      <input
                        type="range"
                        min="0"
                        max="100"
                        value={layer.opacity}
                        className="flex-1 h-1 bg-gray-600 rounded-lg appearance-none cursor-pointer"
                      />
                      <span className="text-xs text-gray-400 w-8">{layer.opacity}%</span>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* 分析工具 */}
        <div className="p-4 border-b border-gray-700">
          <h4 className="text-md font-medium text-white mb-3">分析工具</h4>
          <div className="grid grid-cols-2 gap-2">
            {analysisTools.map((tool) => (
              <button
                key={tool.id}
                className="p-3 bg-dark-700 border border-gray-600 rounded-lg text-gray-300 hover:bg-gray-600 hover:text-white transition-all duration-200"
              >
                <tool.icon className="w-5 h-5 mx-auto mb-1" />
                <div className="text-xs">{tool.name}</div>
              </button>
            ))}
          </div>
        </div>

        {/* 统计信息 */}
        <div className="p-4 flex-1">
          <h4 className="text-md font-medium text-white mb-3">地形统计</h4>
          <div className="space-y-3">
            <div className="bg-dark-700 p-3 rounded-lg">
              <div className="text-xs text-gray-400 mb-1">高程范围</div>
              <div className="text-sm text-white">1,245m - 1,680m</div>
            </div>
            
            <div className="bg-dark-700 p-3 rounded-lg">
              <div className="text-xs text-gray-400 mb-1">平均坡度</div>
              <div className="text-sm text-white">12.5°</div>
            </div>
            
            <div className="bg-dark-700 p-3 rounded-lg">
              <div className="text-xs text-gray-400 mb-1">覆盖面积</div>
              <div className="text-sm text-white">25.6 km²</div>
            </div>
            
            <div className="bg-dark-700 p-3 rounded-lg">
              <div className="text-xs text-gray-400 mb-1">数据精度</div>
              <div className="text-sm text-white">1m × 1m</div>
            </div>
          </div>
        </div>
      </div>

      {/* 主要显示区域 */}
      <div className="flex-1 relative">
        {/* 顶部工具栏 */}
        <div className="absolute top-4 left-4 right-4 z-10">
          <div className="toolbar">
            <div className="flex items-center space-x-2">
              <Ruler className="w-4 h-4 text-gray-400" />
              <span className="text-sm text-gray-300">测量工具</span>
            </div>
            
            <div className="flex items-center space-x-2 text-sm text-gray-300">
              <span>当前图层:</span>
              <select
                value={selectedLayer}
                onChange={(e) => setSelectedLayer(e.target.value)}
                className="bg-dark-700 border border-gray-600 rounded px-2 py-1 text-sm"
              >
                {terrainLayers.map((layer) => (
                  <option key={layer.id} value={layer.id}>{layer.name}</option>
                ))}
              </select>
            </div>
            
            <div className="flex items-center space-x-2 text-sm text-gray-300">
              <span>视角:</span>
              <button className="px-2 py-1 bg-dark-700 border border-gray-600 rounded text-xs hover:bg-gray-600">
                俯视
              </button>
              <button className="px-2 py-1 bg-dark-700 border border-gray-600 rounded text-xs hover:bg-gray-600">
                斜视
              </button>
            </div>
          </div>
        </div>

        {/* 地形显示区域 */}
        <div className="w-full h-full bg-gradient-to-br from-dark-900 to-dark-800 flex items-center justify-center">
          <div className="text-center">
            <Mountain className="w-16 h-16 text-gray-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-400 mb-2">地形数据显示区域</h3>
            <p className="text-gray-500 mb-4">请导入地形数据以开始分析</p>
            <button className="btn-primary">
              <Upload className="w-4 h-4 mr-2" />
              导入DEM数据
            </button>
          </div>
        </div>

        {/* 右下角图例 */}
        <div className="absolute bottom-4 right-4 z-10">
          <div className="panel">
            <div className="panel-header">
              <h5 className="text-sm font-medium text-white">高程图例</h5>
            </div>
            <div className="panel-content">
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-blue-600 rounded"></div>
                  <span className="text-xs text-gray-300">1200-1300m</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-green-600 rounded"></div>
                  <span className="text-xs text-gray-300">1300-1400m</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-yellow-600 rounded"></div>
                  <span className="text-xs text-gray-300">1400-1500m</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-orange-600 rounded"></div>
                  <span className="text-xs text-gray-300">1500-1600m</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-red-600 rounded"></div>
                  <span className="text-xs text-gray-300">1600m+</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default TerrainViewer
