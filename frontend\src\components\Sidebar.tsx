import React from 'react'
import { NavLink } from 'react-router-dom'
import {
  Home,
  FolderOpen,
  Route,
  Mountain,
  Settings,
  Layers,
  Map,
  Ruler,
  Save,
  Upload
} from 'lucide-react'

const Sidebar: React.FC = () => {
  const navItems = [
    { to: '/', icon: Home, label: '仪表板', description: '项目概览和统计' },
    { to: '/projects', icon: FolderOpen, label: '项目管理', description: '创建和管理项目' },
    { to: '/design', icon: Route, label: '道路设计', description: '设计和优化道路' },
    { to: '/terrain', icon: Mountain, label: '地形分析', description: '地形数据和分析' },
    { to: '/settings', icon: Settings, label: '系统设置', description: '应用配置和偏好' },
  ]

  const toolItems = [
    { icon: Layers, label: '图层管理', action: () => {} },
    { icon: Map, label: '地图工具', action: () => {} },
    { icon: Ruler, label: '测量工具', action: () => {} },
    { icon: Save, label: '保存项目', action: () => {} },
    { icon: Upload, label: '导入数据', action: () => {} },
  ]

  return (
    <div className="sidebar">
      {/* Logo和标题 */}
      <div className="sidebar-header">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gradient-to-br from-primary-400 to-primary-600 rounded-lg flex items-center justify-center">
            <Mountain className="w-5 h-5 text-white" />
          </div>
          <div>
            <h1 className="text-lg font-bold text-white">矿山道路设计</h1>
            <p className="text-xs text-gray-400">v1.0.0</p>
          </div>
        </div>
      </div>

      {/* 导航菜单 */}
      <div className="sidebar-content">
        <nav className="space-y-2">
          <div className="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">
            主要功能
          </div>
          
          {navItems.map((item) => (
            <NavLink
              key={item.to}
              to={item.to}
              className={({ isActive }) =>
                `group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                  isActive
                    ? 'bg-primary-500/20 text-primary-400 border border-primary-500/30'
                    : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                }`
              }
            >
              <item.icon className="w-5 h-5 mr-3 flex-shrink-0" />
              <div className="flex-1 min-w-0">
                <div className="font-medium">{item.label}</div>
                <div className="text-xs text-gray-400 group-hover:text-gray-300 truncate">
                  {item.description}
                </div>
              </div>
            </NavLink>
          ))}
        </nav>

        {/* 工具栏 */}
        <div className="mt-8">
          <div className="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">
            快捷工具
          </div>
          
          <div className="space-y-1">
            {toolItems.map((tool, index) => (
              <button
                key={index}
                onClick={tool.action}
                className="w-full flex items-center px-3 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white rounded-lg transition-all duration-200"
              >
                <tool.icon className="w-4 h-4 mr-3" />
                {tool.label}
              </button>
            ))}
          </div>
        </div>

        {/* 状态信息 */}
        <div className="mt-8 p-3 bg-dark-700 rounded-lg border border-gray-600">
          <div className="text-xs text-gray-400 mb-2">系统状态</div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-xs text-gray-300">服务正常</span>
          </div>
          <div className="flex items-center space-x-2 mt-1">
            <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
            <span className="text-xs text-gray-300">Cesium已加载</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Sidebar
