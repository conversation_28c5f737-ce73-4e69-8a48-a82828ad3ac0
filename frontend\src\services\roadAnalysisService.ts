/**
 * 道路分析服务 - 前端道路设计分析功能
 */

import { Coordinate, RoadDesignParams, RoadAnalysis } from '../types'

export interface PathOptimizationOptions {
  maxGrade: number
  minRadius: number
  avoidSteepSlopes: boolean
  minimizeEarthwork: boolean
  considerDrainage: boolean
}

export interface GeometricAnalysis {
  totalLength: number
  segments: Array<{
    startPoint: Coordinate
    endPoint: Coordinate
    length: number
    grade: number
    bearing: number
    radius?: number
  }>
  grades: {
    min: number
    max: number
    average: number
    distribution: Record<string, number>
  }
  curves: Array<{
    position: Coordinate
    radius: number
    angle: number
    type: 'left' | 'right'
  }>
  elevationProfile: Array<{
    distance: number
    elevation: number
    grade: number
  }>
}

export class RoadAnalysisService {
  /**
   * 计算两点间距离
   */
  static calculateDistance(p1: Coordinate, p2: Coordinate): number {
    const R = 6371000 // 地球半径（米）
    const lat1 = p1.latitude * Math.PI / 180
    const lat2 = p2.latitude * Math.PI / 180
    const deltaLat = (p2.latitude - p1.latitude) * Math.PI / 180
    const deltaLon = (p2.longitude - p1.longitude) * Math.PI / 180

    const a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) +
              Math.cos(lat1) * Math.cos(lat2) *
              Math.sin(deltaLon / 2) * Math.sin(deltaLon / 2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))

    return R * c
  }

  /**
   * 计算坡度
   */
  static calculateGrade(p1: Coordinate, p2: Coordinate): number {
    const horizontalDistance = this.calculateDistance(p1, p2)
    if (horizontalDistance === 0) return 0

    const verticalDistance = (p2.elevation || 0) - (p1.elevation || 0)
    return (verticalDistance / horizontalDistance) * 100
  }

  /**
   * 计算方位角
   */
  static calculateBearing(p1: Coordinate, p2: Coordinate): number {
    const lat1 = p1.latitude * Math.PI / 180
    const lat2 = p2.latitude * Math.PI / 180
    const deltaLon = (p2.longitude - p1.longitude) * Math.PI / 180

    const y = Math.sin(deltaLon) * Math.cos(lat2)
    const x = Math.cos(lat1) * Math.sin(lat2) - 
              Math.sin(lat1) * Math.cos(lat2) * Math.cos(deltaLon)

    const bearing = Math.atan2(y, x) * 180 / Math.PI
    return (bearing + 360) % 360
  }

  /**
   * 计算曲线半径
   */
  static calculateCurveRadius(p1: Coordinate, p2: Coordinate, p3: Coordinate): number {
    const a = this.calculateDistance(p1, p2)
    const b = this.calculateDistance(p2, p3)
    const c = this.calculateDistance(p1, p3)

    // 使用海伦公式计算三角形面积
    const s = (a + b + c) / 2
    const area = Math.sqrt(s * (s - a) * (s - b) * (s - c))

    if (area === 0) return Infinity

    // 外接圆半径
    return (a * b * c) / (4 * area)
  }

  /**
   * 分析道路几何特性
   */
  static analyzeGeometry(coordinates: Coordinate[]): GeometricAnalysis {
    if (coordinates.length < 2) {
      throw new Error('至少需要2个坐标点')
    }

    const segments = []
    const curves = []
    const elevationProfile = []
    let totalLength = 0
    const grades: number[] = []

    // 分析每个路段
    for (let i = 0; i < coordinates.length - 1; i++) {
      const startPoint = coordinates[i]
      const endPoint = coordinates[i + 1]
      
      const length = this.calculateDistance(startPoint, endPoint)
      const grade = this.calculateGrade(startPoint, endPoint)
      const bearing = this.calculateBearing(startPoint, endPoint)

      segments.push({
        startPoint,
        endPoint,
        length,
        grade,
        bearing
      })

      totalLength += length
      grades.push(Math.abs(grade))

      // 添加到高程剖面
      elevationProfile.push({
        distance: totalLength - length,
        elevation: startPoint.elevation || 0,
        grade: i > 0 ? grades[i - 1] : 0
      })

      // 分析曲线（需要三个点）
      if (i < coordinates.length - 2) {
        const nextPoint = coordinates[i + 2]
        const radius = this.calculateCurveRadius(startPoint, endPoint, nextPoint)
        
        if (radius < Infinity && radius > 0) {
          // 判断转弯方向
          const angle1 = this.calculateBearing(startPoint, endPoint)
          const angle2 = this.calculateBearing(endPoint, nextPoint)
          let deltaAngle = angle2 - angle1
          
          if (deltaAngle > 180) deltaAngle -= 360
          if (deltaAngle < -180) deltaAngle += 360
          
          curves.push({
            position: endPoint,
            radius,
            angle: Math.abs(deltaAngle),
            type: deltaAngle > 0 ? 'left' : 'right'
          })

          segments[i].radius = radius
        }
      }
    }

    // 添加最后一个点到高程剖面
    elevationProfile.push({
      distance: totalLength,
      elevation: coordinates[coordinates.length - 1].elevation || 0,
      grade: grades[grades.length - 1] || 0
    })

    // 计算坡度分布
    const gradeDistribution: Record<string, number> = {
      '0-3%': 0,
      '3-6%': 0,
      '6-9%': 0,
      '9-12%': 0,
      '>12%': 0
    }

    grades.forEach(grade => {
      if (grade <= 3) gradeDistribution['0-3%']++
      else if (grade <= 6) gradeDistribution['3-6%']++
      else if (grade <= 9) gradeDistribution['6-9%']++
      else if (grade <= 12) gradeDistribution['9-12%']++
      else gradeDistribution['>12%']++
    })

    return {
      totalLength,
      segments,
      grades: {
        min: Math.min(...grades),
        max: Math.max(...grades),
        average: grades.reduce((sum, g) => sum + g, 0) / grades.length,
        distribution: gradeDistribution
      },
      curves,
      elevationProfile
    }
  }

  /**
   * 验证道路设计
   */
  static validateDesign(coordinates: Coordinate[], params: RoadDesignParams): {
    valid: boolean
    violations: string[]
    warnings: string[]
    analysis: GeometricAnalysis
  } {
    const analysis = this.analyzeGeometry(coordinates)
    const violations: string[] = []
    const warnings: string[] = []

    // 检查坡度约束
    if (analysis.grades.max > params.maxGrade) {
      violations.push(`最大坡度 ${analysis.grades.max.toFixed(1)}% 超过限制 ${params.maxGrade}%`)
    }

    if (analysis.grades.max > params.maxGrade * 0.8) {
      warnings.push(`坡度 ${analysis.grades.max.toFixed(1)}% 接近限制值`)
    }

    // 检查转弯半径约束
    const minRadius = Math.min(...analysis.curves.map(c => c.radius))
    if (minRadius < params.minRadius) {
      violations.push(`最小转弯半径 ${minRadius.toFixed(1)}m 小于限制 ${params.minRadius}m`)
    }

    if (minRadius < params.minRadius * 1.2) {
      warnings.push(`转弯半径 ${minRadius.toFixed(1)}m 接近限制值`)
    }

    // 检查设计速度相关约束
    const minRadiusForSpeed = this.calculateMinRadiusForSpeed(params.designSpeed)
    if (minRadius < minRadiusForSpeed) {
      violations.push(`转弯半径不满足设计速度 ${params.designSpeed}km/h 的要求`)
    }

    return {
      valid: violations.length === 0,
      violations,
      warnings,
      analysis
    }
  }

  /**
   * 根据设计速度计算最小转弯半径
   */
  static calculateMinRadiusForSpeed(designSpeed: number): number {
    // 简化的最小半径计算公式
    // R = V²/(127(e + f))
    // 其中 V = 设计速度(km/h), e = 超高(取0.06), f = 横向摩擦系数(取0.15)
    const e = 0.06 // 超高
    const f = 0.15 // 横向摩擦系数
    
    return Math.pow(designSpeed, 2) / (127 * (e + f))
  }

  /**
   * 优化道路路径
   */
  static optimizePath(
    startPoint: Coordinate,
    endPoint: Coordinate,
    options: PathOptimizationOptions,
    terrainData?: number[][]
  ): Coordinate[] {
    // 简化的路径优化算法
    const directDistance = this.calculateDistance(startPoint, endPoint)
    const directGrade = Math.abs(this.calculateGrade(startPoint, endPoint))

    // 如果直线路径满足约束，直接返回
    if (directGrade <= options.maxGrade) {
      return [startPoint, endPoint]
    }

    // 需要增加中间点来降低坡度
    const elevationDiff = Math.abs((endPoint.elevation || 0) - (startPoint.elevation || 0))
    const requiredHorizontalDistance = elevationDiff / (options.maxGrade / 100)
    const segmentCount = Math.ceil(requiredHorizontalDistance / directDistance)

    const optimizedPath: Coordinate[] = [startPoint]

    for (let i = 1; i < segmentCount; i++) {
      const t = i / segmentCount
      const intermediatePoint: Coordinate = {
        longitude: startPoint.longitude + t * (endPoint.longitude - startPoint.longitude),
        latitude: startPoint.latitude + t * (endPoint.latitude - startPoint.latitude),
        elevation: startPoint.elevation! + t * ((endPoint.elevation || 0) - (startPoint.elevation || 0))
      }
      optimizedPath.push(intermediatePoint)
    }

    optimizedPath.push(endPoint)
    return optimizedPath
  }

  /**
   * 计算土方工程量
   */
  static calculateEarthwork(
    coordinates: Coordinate[],
    roadWidth: number,
    designElevations?: number[]
  ): {
    cutVolume: number
    fillVolume: number
    netVolume: number
    segments: Array<{
      distance: number
      cutArea: number
      fillArea: number
      cutVolume: number
      fillVolume: number
    }>
  } {
    const analysis = this.analyzeGeometry(coordinates)
    let totalCutVolume = 0
    let totalFillVolume = 0
    const segments = []

    for (let i = 0; i < analysis.segments.length; i++) {
      const segment = analysis.segments[i]
      const segmentLength = segment.length

      // 简化的土方计算（实际需要详细的横断面设计）
      const avgCutDepth = 2.0 // 平均挖方深度
      const avgFillDepth = 1.5 // 平均填方深度

      const cutArea = roadWidth * avgCutDepth
      const fillArea = roadWidth * avgFillDepth

      const cutVolume = cutArea * segmentLength
      const fillVolume = fillArea * segmentLength

      totalCutVolume += cutVolume
      totalFillVolume += fillVolume

      segments.push({
        distance: i === 0 ? 0 : segments[i - 1].distance + segmentLength,
        cutArea,
        fillArea,
        cutVolume,
        fillVolume
      })
    }

    return {
      cutVolume: totalCutVolume,
      fillVolume: totalFillVolume,
      netVolume: totalCutVolume - totalFillVolume,
      segments
    }
  }

  /**
   * 生成完整的道路分析报告
   */
  static generateAnalysisReport(
    coordinates: Coordinate[],
    params: RoadDesignParams
  ): RoadAnalysis {
    const validation = this.validateDesign(coordinates, params)
    const earthwork = this.calculateEarthwork(coordinates, params.width)

    return {
      totalLength: validation.analysis.totalLength,
      averageGrade: validation.analysis.grades.average,
      maxGrade: validation.analysis.grades.max,
      minGrade: validation.analysis.grades.min,
      totalElevationGain: Math.max(0, 
        (coordinates[coordinates.length - 1].elevation || 0) - (coordinates[0].elevation || 0)
      ),
      totalElevationLoss: Math.max(0, 
        (coordinates[0].elevation || 0) - (coordinates[coordinates.length - 1].elevation || 0)
      ),
      sharpCurves: validation.analysis.curves.filter(c => c.radius < params.minRadius * 1.5).length,
      estimatedCost: this.estimateCost(validation.analysis.totalLength, params, earthwork),
      constructionTime: this.estimateConstructionTime(validation.analysis.totalLength, params),
      earthworkVolume: {
        cut: earthwork.cutVolume,
        fill: earthwork.fillVolume
      }
    }
  }

  /**
   * 估算成本
   */
  private static estimateCost(
    length: number,
    params: RoadDesignParams,
    earthwork: any
  ): number {
    const baseCostPerMeter = {
      dirt: 100,
      gravel: 300,
      asphalt: 800,
      concrete: 1200
    }

    const surfaceCost = length * params.width * (baseCostPerMeter[params.surfaceType as keyof typeof baseCostPerMeter] || 300)
    const earthworkCost = earthwork.cutVolume * 15 + earthwork.fillVolume * 12
    
    return surfaceCost + earthworkCost
  }

  /**
   * 估算施工时间
   */
  private static estimateConstructionTime(length: number, params: RoadDesignParams): number {
    const dailyProgress = {
      dirt: 200,
      gravel: 150,
      asphalt: 100,
      concrete: 80
    }

    const progressPerDay = dailyProgress[params.surfaceType as keyof typeof dailyProgress] || 150
    return Math.ceil(length / progressPerDay)
  }
}
