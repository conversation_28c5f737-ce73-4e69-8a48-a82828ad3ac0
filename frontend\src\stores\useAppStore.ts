import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { User, Project, AppSettings } from '../types'

interface AppState {
  // 用户状态
  user: User | null
  isAuthenticated: boolean
  
  // 当前项目
  currentProject: Project | null
  
  // 应用设置
  settings: AppSettings
  
  // UI状态
  sidebarCollapsed: boolean
  theme: 'light' | 'dark'
  
  // 加载状态
  loading: boolean
  
  // 操作方法
  setUser: (user: User | null) => void
  setCurrentProject: (project: Project | null) => void
  updateSettings: (settings: Partial<AppSettings>) => void
  toggleSidebar: () => void
  setTheme: (theme: 'light' | 'dark') => void
  setLoading: (loading: boolean) => void
  logout: () => void
}

const defaultSettings: AppSettings = {
  language: 'zh-CN',
  theme: 'dark',
  autoSave: true,
  autoSaveInterval: 5,
  cesiumIonToken: '',
  terrainProvider: 'cesium-world-terrain',
  imageryProvider: 'bing-aerial',
  defaultRoadWidth: 6,
  defaultMaxGrade: 8,
  defaultMinRadius: 25,
  defaultSurfaceType: 'asphalt',
  lengthUnit: 'metric',
  angleUnit: 'degrees',
  renderQuality: 'high',
  enableShadows: true,
  enableLighting: true,
  maxFrameRate: 60
}

export const useAppStore = create<AppState>()(
  devtools(
    persist(
      (set, get) => ({
        // 初始状态
        user: null,
        isAuthenticated: false,
        currentProject: null,
        settings: defaultSettings,
        sidebarCollapsed: false,
        theme: 'dark',
        loading: false,

        // 操作方法
        setUser: (user) => set({ 
          user, 
          isAuthenticated: !!user 
        }),

        setCurrentProject: (project) => set({ 
          currentProject: project 
        }),

        updateSettings: (newSettings) => set((state) => ({
          settings: { ...state.settings, ...newSettings }
        })),

        toggleSidebar: () => set((state) => ({
          sidebarCollapsed: !state.sidebarCollapsed
        })),

        setTheme: (theme) => set({ theme }),

        setLoading: (loading) => set({ loading }),

        logout: () => set({
          user: null,
          isAuthenticated: false,
          currentProject: null
        })
      }),
      {
        name: 'app-store',
        partialize: (state) => ({
          settings: state.settings,
          theme: state.theme,
          sidebarCollapsed: state.sidebarCollapsed
        })
      }
    ),
    {
      name: 'app-store'
    }
  )
)
