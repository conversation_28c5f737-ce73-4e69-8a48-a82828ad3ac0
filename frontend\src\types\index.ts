// 项目相关类型定义
export interface Project {
  id: number
  name: string
  description: string
  status: 'planning' | 'designing' | 'reviewing' | 'completed'
  progress: number
  createdAt: string
  lastModified: string
  team: string[]
  location: string
  roads: number
  totalLength: string
}

// 道路相关类型定义
export interface Road {
  id: number
  projectId: number
  name: string
  type: 'main' | 'secondary' | 'access' | 'haul'
  width: number
  length: number
  maxGrade: number
  minRadius: number
  surfaceType: 'asphalt' | 'concrete' | 'gravel' | 'dirt'
  coordinates: Coordinate[]
  elevationProfile: ElevationPoint[]
  status: 'draft' | 'designed' | 'approved'
  createdAt: string
  updatedAt: string
}

// 坐标点类型
export interface Coordinate {
  longitude: number
  latitude: number
  elevation?: number
}

// 高程点类型
export interface ElevationPoint {
  distance: number // 距离起点的距离(m)
  elevation: number // 高程(m)
  grade: number // 坡度(%)
}

// 地形数据类型
export interface TerrainData {
  id: number
  name: string
  type: 'dem' | 'contour' | 'point-cloud'
  format: string
  resolution: number
  extent: {
    minX: number
    maxX: number
    minY: number
    maxY: number
    minZ: number
    maxZ: number
  }
  uploadedAt: string
  fileSize: number
  filePath: string
}

// 用户类型
export interface User {
  id: number
  username: string
  email: string
  fullName: string
  role: 'admin' | 'engineer' | 'viewer'
  avatar?: string
  createdAt: string
  lastLoginAt?: string
}

// API响应类型
export interface ApiResponse<T> {
  success: boolean
  data: T
  message?: string
  errors?: string[]
}

// 分页响应类型
export interface PaginatedResponse<T> {
  success: boolean
  data: T[]
  pagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
  }
}

// 道路设计参数类型
export interface RoadDesignParams {
  width: number
  maxGrade: number
  minRadius: number
  surfaceType: string
  designSpeed: number
  loadCapacity: number
  drainageType: string
  shoulderWidth: number
}

// 地形分析结果类型
export interface TerrainAnalysis {
  slope: {
    min: number
    max: number
    average: number
  }
  elevation: {
    min: number
    max: number
    average: number
  }
  aspect: {
    dominant: string
    distribution: Record<string, number>
  }
  area: number
  volume?: number
}

// 设置类型
export interface AppSettings {
  language: string
  theme: string
  autoSave: boolean
  autoSaveInterval: number
  cesiumIonToken: string
  terrainProvider: string
  imageryProvider: string
  defaultRoadWidth: number
  defaultMaxGrade: number
  defaultMinRadius: number
  defaultSurfaceType: string
  lengthUnit: string
  angleUnit: string
  renderQuality: string
  enableShadows: boolean
  enableLighting: boolean
  maxFrameRate: number
}

// 通知类型
export interface Notification {
  id: number
  type: 'info' | 'warning' | 'error' | 'success'
  title: string
  message: string
  timestamp: string
  read: boolean
  actionUrl?: string
}

// 文件上传类型
export interface FileUpload {
  id: number
  filename: string
  originalName: string
  mimeType: string
  size: number
  uploadedAt: string
  uploadedBy: number
  projectId?: number
  type: 'terrain' | 'image' | 'document' | 'other'
}

// 道路分析结果类型
export interface RoadAnalysis {
  totalLength: number
  averageGrade: number
  maxGrade: number
  minGrade: number
  totalElevationGain: number
  totalElevationLoss: number
  sharpCurves: number
  estimatedCost: number
  constructionTime: number
  earthworkVolume: {
    cut: number
    fill: number
  }
}

// 图层类型
export interface MapLayer {
  id: string
  name: string
  type: 'terrain' | 'imagery' | 'vector' | 'analysis'
  visible: boolean
  opacity: number
  zIndex: number
  source: string
  style?: any
}
