import React, { useRef, useEffect, useState } from 'react'
import { useAppStore } from '../stores/useAppStore'
import { useRoadStore } from '../stores/useRoadStore'

interface CesiumViewerProps {
  className?: string
  onViewerReady?: (viewer: any) => void
  onCoordinateClick?: (coordinate: { longitude: number; latitude: number; elevation?: number }) => void
}

export const CesiumViewer: React.FC<CesiumViewerProps> = ({
  className = '',
  onViewerReady,
  onCoordinateClick
}) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const viewerRef = useRef<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  const { settings } = useAppStore()
  const { isDrawing, selectedTool, addCoordinate } = useRoadStore()

  useEffect(() => {
    if (!containerRef.current || viewerRef.current) return

    const initializeCesium = async () => {
      try {
        // 检查Cesium是否可用
        if (typeof (window as any).Cesium === 'undefined') {
          throw new Error('Cesium未加载')
        }

        const Cesium = (window as any).Cesium

        // 设置Cesium Ion访问令牌
        if (settings.cesiumIonToken) {
          Cesium.Ion.defaultAccessToken = settings.cesiumIonToken
        }

        // 创建地形提供者
        let terrainProvider
        switch (settings.terrainProvider) {
          case 'cesium-world-terrain':
            terrainProvider = Cesium.createWorldTerrain()
            break
          case 'ellipsoid':
            terrainProvider = new Cesium.EllipsoidTerrainProvider()
            break
          default:
            terrainProvider = new Cesium.EllipsoidTerrainProvider()
        }

        // 创建影像提供者
        let imageryProvider
        switch (settings.imageryProvider) {
          case 'bing-aerial':
            imageryProvider = new Cesium.BingMapsImageryProvider({
              url: 'https://dev.virtualearth.net',
              mapStyle: Cesium.BingMapsStyle.AERIAL_WITH_LABELS
            })
            break
          case 'osm':
            imageryProvider = new Cesium.OpenStreetMapImageryProvider({
              url: 'https://a.tile.openstreetmap.org/'
            })
            break
          default:
            imageryProvider = new Cesium.BingMapsImageryProvider({
              url: 'https://dev.virtualearth.net',
              mapStyle: Cesium.BingMapsStyle.AERIAL_WITH_LABELS
            })
        }

        // 创建Cesium viewer
        const viewer = new Cesium.Viewer(containerRef.current, {
          terrainProvider,
          imageryProvider,
          baseLayerPicker: false,
          geocoder: false,
          homeButton: false,
          sceneModePicker: false,
          navigationHelpButton: false,
          animation: false,
          timeline: false,
          fullscreenButton: false,
          vrButton: false,
          infoBox: true,
          selectionIndicator: true,
          shadows: settings.enableShadows,
          shouldAnimate: true
        })

        // 配置渲染质量
        const scene = viewer.scene
        switch (settings.renderQuality) {
          case 'low':
            scene.fxaa = false
            scene.postProcessStages.fxaa.enabled = false
            break
          case 'medium':
            scene.fxaa = true
            scene.postProcessStages.fxaa.enabled = true
            break
          case 'high':
          case 'ultra':
            scene.fxaa = true
            scene.postProcessStages.fxaa.enabled = true
            scene.globe.enableLighting = settings.enableLighting
            break
        }

        // 设置初始视角（中国某个矿山区域）
        viewer.camera.setView({
          destination: Cesium.Cartesian3.fromDegrees(116.4, 39.9, 2000),
          orientation: {
            heading: Cesium.Math.toRadians(0),
            pitch: Cesium.Math.toRadians(-45),
            roll: 0.0
          }
        })

        // 添加点击事件处理
        viewer.cesiumWidget.screenSpaceEventHandler.setInputAction((event: any) => {
          const pickedPosition = viewer.camera.pickEllipsoid(event.position, scene.globe.ellipsoid)
          
          if (pickedPosition) {
            const cartographic = Cesium.Cartographic.fromCartesian(pickedPosition)
            const longitude = Cesium.Math.toDegrees(cartographic.longitude)
            const latitude = Cesium.Math.toDegrees(cartographic.latitude)
            const elevation = cartographic.height

            const coordinate = { longitude, latitude, elevation }

            // 如果正在绘制道路，添加坐标点
            if (isDrawing && selectedTool === 'draw') {
              addCoordinate(coordinate)
              
              // 在地图上添加点标记
              viewer.entities.add({
                position: pickedPosition,
                point: {
                  pixelSize: 8,
                  color: Cesium.Color.YELLOW,
                  outlineColor: Cesium.Color.BLACK,
                  outlineWidth: 2,
                  heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
                }
              })
            }

            // 触发坐标点击回调
            if (onCoordinateClick) {
              onCoordinateClick(coordinate)
            }
          }
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK)

        viewerRef.current = viewer
        setIsLoading(false)

        // 触发准备就绪回调
        if (onViewerReady) {
          onViewerReady(viewer)
        }

      } catch (err) {
        console.error('Cesium初始化失败:', err)
        setError(err instanceof Error ? err.message : '未知错误')
        setIsLoading(false)
      }
    }

    initializeCesium()

    // 清理函数
    return () => {
      if (viewerRef.current) {
        viewerRef.current.destroy()
        viewerRef.current = null
      }
    }
  }, [settings, onViewerReady, onCoordinateClick, isDrawing, selectedTool, addCoordinate])

  // 渲染加载状态
  if (isLoading) {
    return (
      <div className={`flex items-center justify-center bg-dark-900 ${className}`}>
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-gray-600 border-t-primary-500 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-400">正在加载三维地球引擎...</p>
          <p className="text-xs text-gray-500 mt-2">请确保网络连接正常</p>
        </div>
      </div>
    )
  }

  // 渲染错误状态
  if (error) {
    return (
      <div className={`flex items-center justify-center bg-dark-900 ${className}`}>
        <div className="text-center">
          <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-red-400 text-2xl">⚠</span>
          </div>
          <p className="text-red-400 mb-2">Cesium加载失败</p>
          <p className="text-xs text-gray-500">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors"
          >
            重新加载
          </button>
        </div>
      </div>
    )
  }

  return (
    <div
      ref={containerRef}
      className={`cesium-viewer-container ${className}`}
      style={{ width: '100%', height: '100%' }}
    />
  )
}
