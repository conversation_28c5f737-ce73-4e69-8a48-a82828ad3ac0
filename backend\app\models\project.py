"""
项目数据模型
"""

from sqlalchemy import Column, String, Text, Float, Integer, ForeignKey, Table, Enum
from sqlalchemy.orm import relationship
import enum

from .base import BaseModel


class ProjectStatus(str, enum.Enum):
    """项目状态枚举"""
    PLANNING = "planning"
    DESIGNING = "designing"
    REVIEWING = "reviewing"
    COMPLETED = "completed"


# 项目成员关联表
project_members = Table(
    'project_members',
    BaseModel.metadata,
    Column('project_id', Integer, ForeignKey('projects.id'), primary_key=True),
    Column('user_id', Integer, ForeignKey('users.id'), primary_key=True)
)


class Project(BaseModel):
    """项目模型"""
    __tablename__ = "projects"
    
    name = Column(String(200), nullable=False, index=True)
    description = Column(Text, nullable=True)
    location = Column(String(200), nullable=False)
    status = Column(Enum(ProjectStatus), default=ProjectStatus.PLANNING, nullable=False)
    progress = Column(Float, default=0.0, nullable=False)
    
    # 外键
    creator_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    
    # 关联关系
    creator = relationship("User", back_populates="created_projects")
    team_members = relationship("User", secondary=project_members, back_populates="projects")
    roads = relationship("Road", back_populates="project", cascade="all, delete-orphan")
    terrain_data = relationship("TerrainData", back_populates="project", cascade="all, delete-orphan")
    uploads = relationship("FileUpload", back_populates="project", cascade="all, delete-orphan")
    
    @property
    def roads_count(self) -> int:
        """道路数量"""
        return len(self.roads)
    
    @property
    def total_length(self) -> float:
        """总长度"""
        return sum(road.length for road in self.roads)
