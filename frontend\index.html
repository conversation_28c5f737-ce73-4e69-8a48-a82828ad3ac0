<!doctype html>
<html lang="zh-CN" class="dark">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>露天矿山道路设计系统</title>
    <meta name="description" content="基于Cesium的现代化露天矿山道路设计Web应用" />
    
    <!-- Cesium CSS -->
    <script src="https://cesium.com/downloads/cesiumjs/releases/1.111/Build/Cesium/Cesium.js"></script>
    <link href="https://cesium.com/downloads/cesiumjs/releases/1.111/Build/Cesium/Widgets/widgets.css" rel="stylesheet">
    
    <!-- 字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
      /* 全局样式重置 */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: 'Inter', system-ui, -apple-system, sans-serif;
        background-color: #0f172a;
        color: #f1f5f9;
        overflow: hidden;
      }
      
      /* 加载动画 */
      .loading-spinner {
        display: inline-block;
        width: 40px;
        height: 40px;
        border: 3px solid #374151;
        border-radius: 50%;
        border-top-color: #f59e0b;
        animation: spin 1s ease-in-out infinite;
      }
      
      @keyframes spin {
        to { transform: rotate(360deg); }
      }
      
      /* Cesium样式覆盖 */
      .cesium-widget-credits {
        display: none !important;
      }
      
      .cesium-viewer-toolbar {
        background: rgba(15, 23, 42, 0.8) !important;
        border-radius: 8px !important;
        backdrop-filter: blur(10px) !important;
      }
      
      .cesium-button {
        background: rgba(55, 65, 81, 0.8) !important;
        border: 1px solid rgba(107, 114, 128, 0.3) !important;
        color: #f1f5f9 !important;
      }
      
      .cesium-button:hover {
        background: rgba(245, 158, 11, 0.2) !important;
        border-color: #f59e0b !important;
      }
    </style>
  </head>
  <body>
    <div id="root">
      <!-- 加载页面 -->
      <div style="
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100vh;
        background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
      ">
        <div class="loading-spinner"></div>
        <p style="margin-top: 20px; color: #94a3b8; font-size: 14px;">
          正在加载露天矿山道路设计系统...
        </p>
      </div>
    </div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
