import React from 'react'
import {
  Save,
  RefreshCw,
  Globe,
  Palette,
  Settings as SettingsIcon,
  User,
  Shield,
  Database
} from 'lucide-react'

const Settings: React.FC = () => {
  const [settings, setSettings] = React.useState({
    // 系统设置
    language: 'zh-CN',
    theme: 'dark',
    autoSave: true,
    autoSaveInterval: 5,
    
    // Cesium设置
    cesiumIonToken: '',
    terrainProvider: 'cesium-world-terrain',
    imageryProvider: 'bing-aerial',
    
    // 道路设计默认参数
    defaultRoadWidth: 6,
    defaultMaxGrade: 8,
    defaultMinRadius: 25,
    defaultSurfaceType: 'asphalt',
    
    // 单位设置
    lengthUnit: 'metric',
    angleUnit: 'degrees',
    
    // 性能设置
    renderQuality: 'high',
    enableShadows: true,
    enableLighting: true,
    maxFrameRate: 60
  })

  const settingsSections = [
    {
      id: 'general',
      title: '常规设置',
      icon: SettingsIcon,
      items: [
        {
          key: 'language',
          label: '界面语言',
          type: 'select',
          options: [
            { value: 'zh-CN', label: '简体中文' },
            { value: 'en-US', label: 'English' }
          ]
        },
        {
          key: 'theme',
          label: '主题',
          type: 'select',
          options: [
            { value: 'dark', label: '深色主题' },
            { value: 'light', label: '浅色主题' }
          ]
        },
        {
          key: 'autoSave',
          label: '自动保存',
          type: 'checkbox'
        },
        {
          key: 'autoSaveInterval',
          label: '自动保存间隔(分钟)',
          type: 'number',
          min: 1,
          max: 60
        }
      ]
    },
    {
      id: 'cesium',
      title: 'Cesium配置',
      icon: Globe,
      items: [
        {
          key: 'cesiumIonToken',
          label: 'Cesium Ion访问令牌',
          type: 'text',
          placeholder: '输入您的Cesium Ion访问令牌'
        },
        {
          key: 'terrainProvider',
          label: '地形数据源',
          type: 'select',
          options: [
            { value: 'cesium-world-terrain', label: 'Cesium World Terrain' },
            { value: 'ellipsoid', label: '椭球体' },
            { value: 'custom', label: '自定义' }
          ]
        },
        {
          key: 'imageryProvider',
          label: '影像数据源',
          type: 'select',
          options: [
            { value: 'bing-aerial', label: 'Bing Maps 卫星影像' },
            { value: 'osm', label: 'OpenStreetMap' },
            { value: 'custom', label: '自定义' }
          ]
        }
      ]
    },
    {
      id: 'road-defaults',
      title: '道路设计默认值',
      icon: Palette,
      items: [
        {
          key: 'defaultRoadWidth',
          label: '默认道路宽度(m)',
          type: 'number',
          min: 3,
          max: 20,
          step: 0.5
        },
        {
          key: 'defaultMaxGrade',
          label: '默认最大坡度(%)',
          type: 'number',
          min: 1,
          max: 15,
          step: 0.5
        },
        {
          key: 'defaultMinRadius',
          label: '默认最小转弯半径(m)',
          type: 'number',
          min: 10,
          max: 100,
          step: 5
        },
        {
          key: 'defaultSurfaceType',
          label: '默认路面类型',
          type: 'select',
          options: [
            { value: 'asphalt', label: '沥青路面' },
            { value: 'concrete', label: '混凝土路面' },
            { value: 'gravel', label: '碎石路面' },
            { value: 'dirt', label: '土路' }
          ]
        }
      ]
    },
    {
      id: 'performance',
      title: '性能设置',
      icon: Database,
      items: [
        {
          key: 'renderQuality',
          label: '渲染质量',
          type: 'select',
          options: [
            { value: 'low', label: '低' },
            { value: 'medium', label: '中' },
            { value: 'high', label: '高' },
            { value: 'ultra', label: '超高' }
          ]
        },
        {
          key: 'enableShadows',
          label: '启用阴影',
          type: 'checkbox'
        },
        {
          key: 'enableLighting',
          label: '启用光照',
          type: 'checkbox'
        },
        {
          key: 'maxFrameRate',
          label: '最大帧率',
          type: 'number',
          min: 30,
          max: 120,
          step: 10
        }
      ]
    }
  ]

  const handleSettingChange = (key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const handleSave = () => {
    // 保存设置到本地存储或服务器
    localStorage.setItem('app-settings', JSON.stringify(settings))
    // 显示保存成功提示
  }

  const handleReset = () => {
    // 重置为默认设置
    if (confirm('确定要重置所有设置为默认值吗？')) {
      // 重置逻辑
    }
  }

  const renderSettingItem = (item: any) => {
    const value = settings[item.key as keyof typeof settings]

    switch (item.type) {
      case 'text':
        return (
          <input
            type="text"
            value={value as string}
            onChange={(e) => handleSettingChange(item.key, e.target.value)}
            placeholder={item.placeholder}
            className="input"
          />
        )
      
      case 'number':
        return (
          <input
            type="number"
            value={value as number}
            onChange={(e) => handleSettingChange(item.key, Number(e.target.value))}
            min={item.min}
            max={item.max}
            step={item.step}
            className="input"
          />
        )
      
      case 'select':
        return (
          <select
            value={value as string}
            onChange={(e) => handleSettingChange(item.key, e.target.value)}
            className="input"
          >
            {item.options.map((option: any) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        )
      
      case 'checkbox':
        return (
          <label className="flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={value as boolean}
              onChange={(e) => handleSettingChange(item.key, e.target.checked)}
              className="w-4 h-4 text-primary-500 bg-dark-700 border-gray-600 rounded focus:ring-primary-500"
            />
            <span className="ml-2 text-sm text-gray-300">启用</span>
          </label>
        )
      
      default:
        return null
    }
  }

  return (
    <div className="p-6 h-full overflow-y-auto">
      {/* 页面标题 */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-white">系统设置</h1>
          <p className="text-gray-400 mt-1">配置应用程序的各项参数和偏好设置</p>
        </div>
        <div className="flex items-center space-x-3">
          <button onClick={handleReset} className="btn-secondary">
            <RefreshCw className="w-4 h-4 mr-2" />
            重置默认
          </button>
          <button onClick={handleSave} className="btn-primary">
            <Save className="w-4 h-4 mr-2" />
            保存设置
          </button>
        </div>
      </div>

      {/* 设置内容 */}
      <div className="space-y-6">
        {settingsSections.map((section) => (
          <div key={section.id} className="card">
            <div className="card-header">
              <div className="flex items-center space-x-3">
                <section.icon className="w-5 h-5 text-primary-400" />
                <h3 className="text-lg font-semibold text-white">{section.title}</h3>
              </div>
            </div>
            <div className="card-body">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {section.items.map((item) => (
                  <div key={item.key} className="space-y-2">
                    <label className="block text-sm font-medium text-gray-300">
                      {item.label}
                    </label>
                    {renderSettingItem(item)}
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 底部信息 */}
      <div className="mt-8 p-4 bg-dark-700 rounded-lg border border-gray-600">
        <div className="flex items-center justify-between text-sm text-gray-400">
          <div>
            <p>露天矿山道路设计系统 v1.0.0</p>
            <p>最后更新: 2024年1月25日</p>
          </div>
          <div className="text-right">
            <p>技术支持: <EMAIL></p>
            <p>文档: docs.example.com</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Settings
