"""
API v1 路由汇总
"""

from fastapi import APIRouter

from app.api.api_v1.endpoints import (
    auth,
    users,
    projects,
    roads,
    terrain,
    uploads
)

# 创建API路由器
api_router = APIRouter()

# 注册各个模块的路由
api_router.include_router(auth.router, prefix="/auth", tags=["认证"])
api_router.include_router(users.router, prefix="/users", tags=["用户管理"])
api_router.include_router(projects.router, prefix="/projects", tags=["项目管理"])
api_router.include_router(roads.router, prefix="/roads", tags=["道路设计"])
api_router.include_router(terrain.router, prefix="/terrain", tags=["地形数据"])
api_router.include_router(uploads.router, prefix="/uploads", tags=["文件上传"])
