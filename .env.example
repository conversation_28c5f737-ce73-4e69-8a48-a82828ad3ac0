# 环境配置示例文件
# 复制此文件为 .env 并修改相应的配置值

# 应用环境
ENVIRONMENT=development
DEBUG=true

# 数据库配置
POSTGRES_SERVER=localhost
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_DB=openpit_road_design
POSTGRES_PORT=5432

# Redis配置
REDIS_URL=redis://localhost:6379

# JWT配置
SECRET_KEY=your-secret-key-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 文件上传配置
MAX_FILE_SIZE=104857600
UPLOAD_DIR=uploads

# Cesium配置
CESIUM_ION_ACCESS_TOKEN=your-cesium-ion-access-token

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# 跨域配置
ALLOWED_HOSTS=["http://localhost:5173", "http://127.0.0.1:5173"]
