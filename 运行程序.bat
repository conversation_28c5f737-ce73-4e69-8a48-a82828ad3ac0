@echo off
chcp 65001 >nul
title 露天矿山道路设计系统 - 启动器

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    露天矿山道路设计系统                        ║
echo ║                      程序启动器 v1.0                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🚀 正在启动演示程序...
echo.

REM 尝试打开启动页面
echo [1/3] 尝试打开启动页面...
start "" "🚀 点击这里运行程序.html"
timeout /t 2 /nobreak >nul

REM 备用方案：直接打开演示页面
echo [2/3] 备用方案：直接打开演示页面...
start "" "simple-demo.html"
timeout /t 2 /nobreak >nul

REM 最后方案：打开文件夹
echo [3/3] 打开项目文件夹...
start "" "%~dp0"

echo.
echo ✅ 启动完成！
echo.
echo 📋 使用说明：
echo    1. 如果浏览器已打开页面，请在浏览器中查看
echo    2. 如果没有自动打开，请手动双击以下文件：
echo       • 🚀 点击这里运行程序.html （启动页面）
echo       • simple-demo.html （直接演示）
echo.
echo 📖 更多帮助：
echo    • 查看 "如何运行程序.txt" 获取详细说明
echo    • 查看 "PROJECT_STATUS.md" 了解项目状态
echo    • 查看 "INSTALLATION.md" 安装完整版本
echo.
echo 🎯 演示功能：
echo    ✓ 三维地形界面设计
echo    ✓ 道路设计工具面板
echo    ✓ 参数配置和实时分析
echo    ✓ 项目管理功能演示
echo.

pause
echo.
echo 感谢使用露天矿山道路设计系统！🏔️
timeout /t 3 /nobreak >nul
