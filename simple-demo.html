<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>露天矿山道路设计系统 - 简化演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: white;
            height: 100vh;
            overflow: hidden;
        }
        
        .header {
            height: 64px;
            background: rgba(30, 41, 59, 0.95);
            border-bottom: 1px solid #374151;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
            backdrop-filter: blur(10px);
        }
        
        .logo {
            font-size: 20px;
            font-weight: bold;
            color: #fbbf24;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .avatar {
            width: 32px;
            height: 32px;
            background: linear-gradient(45deg, #fbbf24, #f59e0b);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }
        
        .main-container {
            display: flex;
            height: calc(100vh - 64px);
        }
        
        .sidebar {
            width: 320px;
            background: rgba(30, 41, 59, 0.95);
            border-right: 1px solid #374151;
            padding: 24px;
            overflow-y: auto;
        }
        
        .section {
            margin-bottom: 24px;
            padding-bottom: 24px;
            border-bottom: 1px solid #374151;
        }
        
        .section:last-child {
            border-bottom: none;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            color: white;
        }
        
        .tools-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
        }
        
        .tool-btn {
            padding: 12px;
            background: #334155;
            border: 1px solid #475569;
            border-radius: 8px;
            color: #e2e8f0;
            cursor: pointer;
            transition: all 0.2s;
            text-align: center;
            font-size: 12px;
        }
        
        .tool-btn:hover {
            background: #475569;
            border-color: #64748b;
        }
        
        .tool-btn.active {
            background: rgba(251, 191, 36, 0.2);
            border-color: #fbbf24;
            color: #fbbf24;
        }
        
        .tool-icon {
            font-size: 20px;
            margin-bottom: 4px;
            display: block;
        }
        
        .param-group {
            margin-bottom: 16px;
        }
        
        .param-label {
            display: block;
            font-size: 14px;
            color: #d1d5db;
            margin-bottom: 4px;
        }
        
        .param-input {
            width: 100%;
            padding: 8px 12px;
            background: #334155;
            border: 1px solid #475569;
            border-radius: 6px;
            color: white;
            font-size: 14px;
        }
        
        .param-input:focus {
            outline: none;
            border-color: #fbbf24;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            margin-bottom: 16px;
        }
        
        .stat-card {
            background: #334155;
            padding: 12px;
            border-radius: 6px;
            text-align: center;
        }
        
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #fbbf24;
        }
        
        .stat-label {
            font-size: 12px;
            color: #9ca3af;
            margin-top: 4px;
        }
        
        .status-card {
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid rgba(34, 197, 94, 0.3);
            padding: 12px;
            border-radius: 6px;
            text-align: center;
        }
        
        .status-text {
            color: #22c55e;
            font-size: 14px;
            font-weight: 500;
        }
        
        .main-view {
            flex: 1;
            position: relative;
            background: linear-gradient(45deg, #1e293b 0%, #334155 100%);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .toolbar {
            position: absolute;
            top: 16px;
            left: 16px;
            display: flex;
            gap: 8px;
            background: rgba(30, 41, 59, 0.9);
            padding: 8px;
            border-radius: 8px;
            border: 1px solid #374151;
        }
        
        .toolbar-btn {
            width: 40px;
            height: 40px;
            background: #475569;
            border: none;
            border-radius: 6px;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }
        
        .toolbar-btn:hover {
            background: #64748b;
        }
        
        .toolbar-btn.active {
            background: #fbbf24;
            color: #0f172a;
        }
        
        .map-placeholder {
            text-align: center;
            color: #9ca3af;
        }
        
        .map-icon {
            font-size: 64px;
            margin-bottom: 16px;
            opacity: 0.5;
        }
        
        .preview-panel {
            position: absolute;
            bottom: 16px;
            right: 16px;
            width: 300px;
            background: rgba(30, 41, 59, 0.95);
            border: 1px solid #374151;
            border-radius: 8px;
            padding: 16px;
        }
        
        .preview-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 12px;
            color: white;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .coordinate-display {
            position: absolute;
            top: 16px;
            right: 16px;
            background: rgba(30, 41, 59, 0.9);
            padding: 12px;
            border-radius: 8px;
            border: 1px solid #374151;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: #fbbf24;
            color: #0f172a;
        }
        
        .btn-primary:hover {
            background: #f59e0b;
        }
        
        .btn-secondary {
            background: #475569;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #64748b;
        }
        
        .road-demo {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 400px;
            height: 200px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px dashed #475569;
        }
        
        .road-path {
            width: 300px;
            height: 4px;
            background: linear-gradient(90deg, #fbbf24 0%, #f59e0b 50%, #fbbf24 100%);
            border-radius: 2px;
            position: relative;
            animation: roadGlow 2s ease-in-out infinite alternate;
        }
        
        @keyframes roadGlow {
            0% { box-shadow: 0 0 5px rgba(251, 191, 36, 0.5); }
            100% { box-shadow: 0 0 20px rgba(251, 191, 36, 0.8); }
        }
        
        .road-path::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(90deg, transparent, rgba(251, 191, 36, 0.3), transparent);
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">🏔️ 露天矿山道路设计系统</div>
        <div class="user-info">
            <div>
                <div style="font-size: 14px; font-weight: 500;">演示项目</div>
                <div style="font-size: 12px; color: #9ca3af;">最后保存: 刚刚</div>
            </div>
            <div class="avatar">演</div>
        </div>
    </div>

    <div class="main-container">
        <div class="sidebar">
            <div class="section">
                <div class="section-title">🛠️ 设计工具</div>
                <div class="tools-grid">
                    <div class="tool-btn active" onclick="selectTool(this, 'select')">
                        <span class="tool-icon">👆</span>
                        选择
                    </div>
                    <div class="tool-btn" onclick="selectTool(this, 'move')">
                        <span class="tool-icon">✋</span>
                        移动
                    </div>
                    <div class="tool-btn" onclick="selectTool(this, 'draw')">
                        <span class="tool-icon">✏️</span>
                        绘制道路
                    </div>
                    <div class="tool-btn" onclick="selectTool(this, 'measure')">
                        <span class="tool-icon">📏</span>
                        测量
                    </div>
                </div>
            </div>

            <div class="section">
                <div class="section-title">⚙️ 道路参数</div>
                <div class="param-group">
                    <label class="param-label">道路宽度 (m)</label>
                    <input type="number" class="param-input" value="6" onchange="updateAnalysis()">
                </div>
                <div class="param-group">
                    <label class="param-label">最大坡度 (%)</label>
                    <input type="number" class="param-input" value="8" onchange="updateAnalysis()">
                </div>
                <div class="param-group">
                    <label class="param-label">最小转弯半径 (m)</label>
                    <input type="number" class="param-input" value="25" onchange="updateAnalysis()">
                </div>
                <div class="param-group">
                    <label class="param-label">路面类型</label>
                    <select class="param-input" onchange="updateAnalysis()">
                        <option value="asphalt">沥青路面</option>
                        <option value="concrete">混凝土路面</option>
                        <option value="gravel" selected>碎石路面</option>
                        <option value="dirt">土路</option>
                    </select>
                </div>
            </div>

            <div class="section">
                <div class="section-title">📊 分析结果</div>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value" id="totalLength">2.45</div>
                        <div class="stat-label">总长度 (km)</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="avgGrade">5.2</div>
                        <div class="stat-label">平均坡度 (%)</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="maxGrade">7.8</div>
                        <div class="stat-label">最大坡度 (%)</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="estimatedCost">125</div>
                        <div class="stat-label">估算成本 (万元)</div>
                    </div>
                </div>
                <div class="status-card">
                    <div class="status-text">✅ 设计符合规范</div>
                </div>
            </div>

            <div class="section">
                <div class="section-title">💾 项目操作</div>
                <button class="btn btn-primary" style="width: 100%; margin-bottom: 8px;" onclick="saveProject()">
                    💾 保存项目
                </button>
                <button class="btn btn-secondary" style="width: 100%;" onclick="exportReport()">
                    📄 导出报告
                </button>
            </div>
        </div>

        <div class="main-view">
            <div class="toolbar">
                <button class="toolbar-btn active" title="选择工具">👆</button>
                <button class="toolbar-btn" title="绘制道路">✏️</button>
                <button class="toolbar-btn" title="重置视图">🔄</button>
                <button class="toolbar-btn" title="放大">🔍</button>
            </div>

            <div class="coordinate-display">
                <div>📍 坐标: 116.4000°E, 39.9000°N</div>
                <div>📏 高程: 1,245m</div>
                <div>🎯 比例: 1:5000</div>
            </div>

            <div class="road-demo">
                <div class="road-path"></div>
            </div>

            <div class="map-placeholder">
                <div class="map-icon">🗺️</div>
                <h3>三维地形视图</h3>
                <p>这里将显示基于Cesium的三维地球引擎</p>
                <p style="margin-top: 8px; font-size: 14px;">
                    完整版本需要配置Cesium Ion访问令牌
                </p>
            </div>

            <div class="preview-panel">
                <div class="preview-title">
                    📈 实时预览
                </div>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value">2.45</div>
                        <div class="stat-label">长度 (km)</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">5.2</div>
                        <div class="stat-label">坡度 (%)</div>
                    </div>
                </div>
                <div style="background: rgba(34, 197, 94, 0.1); border: 1px solid rgba(34, 197, 94, 0.3); padding: 8px; border-radius: 4px; margin-top: 8px;">
                    <div style="color: #22c55e; font-size: 12px;">✅ 设计验证通过</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentTool = 'select';
        
        function selectTool(element, tool) {
            // 移除所有active类
            document.querySelectorAll('.tool-btn').forEach(btn => btn.classList.remove('active'));
            // 添加active类到当前按钮
            element.classList.add('active');
            currentTool = tool;
            
            // 更新工具栏
            document.querySelectorAll('.toolbar-btn').forEach(btn => btn.classList.remove('active'));
            if (tool === 'select') document.querySelector('.toolbar-btn').classList.add('active');
            
            console.log('选择工具:', tool);
        }
        
        function updateAnalysis() {
            // 模拟分析更新
            const width = parseFloat(document.querySelector('input[type="number"]').value);
            const grade = parseFloat(document.querySelectorAll('input[type="number"]')[1].value);
            
            // 更新显示值
            document.getElementById('totalLength').textContent = (2.45 + Math.random() * 0.1).toFixed(2);
            document.getElementById('avgGrade').textContent = (grade * 0.65).toFixed(1);
            document.getElementById('maxGrade').textContent = (grade * 0.98).toFixed(1);
            document.getElementById('estimatedCost').textContent = Math.round(width * 20.8);
            
            console.log('分析已更新');
        }
        
        function saveProject() {
            alert('项目保存成功！\n\n这是演示版本，实际版本会保存到数据库。');
        }
        
        function exportReport() {
            alert('报告导出功能\n\n实际版本会生成详细的PDF报告，包含：\n- 道路设计图纸\n- 工程量统计\n- 成本分析\n- 技术参数');
        }
        
        // 模拟实时坐标更新
        setInterval(() => {
            const coordDisplay = document.querySelector('.coordinate-display');
            const lat = (39.9 + (Math.random() - 0.5) * 0.001).toFixed(4);
            const lng = (116.4 + (Math.random() - 0.5) * 0.001).toFixed(4);
            const elevation = Math.round(1245 + (Math.random() - 0.5) * 10);
            
            coordDisplay.innerHTML = `
                <div>📍 坐标: ${lng}°E, ${lat}°N</div>
                <div>📏 高程: ${elevation}m</div>
                <div>🎯 比例: 1:5000</div>
            `;
        }, 2000);
        
        console.log('露天矿山道路设计系统演示已加载');
        console.log('当前工具:', currentTool);
    </script>
</body>
</html>
