"""
用户管理相关API端点
"""

from fastapi import APIRouter
from pydantic import BaseModel
from typing import List, Optional

router = APIRouter()


class UserResponse(BaseModel):
    """用户响应模型"""
    id: int
    username: str
    email: str
    full_name: str
    role: str
    avatar: Optional[str] = None
    is_active: bool
    created_at: str


@router.get("/", response_model=List[UserResponse])
async def get_users():
    """
    获取用户列表
    """
    # TODO: 实现用户列表查询
    return [
        {
            "id": 1,
            "username": "admin",
            "email": "<EMAIL>",
            "full_name": "系统管理员",
            "role": "admin",
            "avatar": None,
            "is_active": True,
            "created_at": "2024-01-01T00:00:00Z"
        }
    ]


@router.get("/{user_id}", response_model=UserResponse)
async def get_user(user_id: int):
    """
    获取用户详情
    """
    # TODO: 实现用户详情查询
    return {
        "id": user_id,
        "username": "admin",
        "email": "<EMAIL>",
        "full_name": "系统管理员",
        "role": "admin",
        "avatar": None,
        "is_active": True,
        "created_at": "2024-01-01T00:00:00Z"
    }
