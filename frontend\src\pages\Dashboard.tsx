import React from 'react'
import {
  BarChart3,
  TrendingUp,
  MapPin,
  Clock,
  Users,
  FileText,
  AlertTriangle,
  CheckCircle
} from 'lucide-react'

const Dashboard: React.FC = () => {
  const stats = [
    {
      title: '活跃项目',
      value: '12',
      change: '+2',
      changeType: 'increase',
      icon: FileText,
      color: 'text-blue-400'
    },
    {
      title: '设计道路',
      value: '48',
      change: '+8',
      changeType: 'increase',
      icon: MapPin,
      color: 'text-green-400'
    },
    {
      title: '团队成员',
      value: '6',
      change: '0',
      changeType: 'neutral',
      icon: Users,
      color: 'text-purple-400'
    },
    {
      title: '本月完成',
      value: '24',
      change: '+12',
      changeType: 'increase',
      icon: CheckCircle,
      color: 'text-primary-400'
    }
  ]

  const recentProjects = [
    {
      id: 1,
      name: '矿山A区主干道设计',
      status: '进行中',
      progress: 75,
      lastModified: '2小时前',
      team: ['张工', '李工', '王工']
    },
    {
      id: 2,
      name: 'B区运输道路优化',
      status: '审核中',
      progress: 90,
      lastModified: '1天前',
      team: ['陈工', '刘工']
    },
    {
      id: 3,
      name: 'C区新建道路规划',
      status: '设计中',
      progress: 45,
      lastModified: '3天前',
      team: ['赵工', '孙工', '周工']
    }
  ]

  const alerts = [
    {
      type: 'warning',
      message: '矿山A区主干道坡度超出安全范围，需要调整',
      time: '30分钟前'
    },
    {
      type: 'info',
      message: 'B区运输道路设计已提交审核',
      time: '2小时前'
    },
    {
      type: 'success',
      message: 'C区地形数据导入完成',
      time: '4小时前'
    }
  ]

  return (
    <div className="p-6 space-y-6 overflow-y-auto h-full">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">仪表板</h1>
          <p className="text-gray-400 mt-1">露天矿山道路设计项目概览</p>
        </div>
        <div className="flex items-center space-x-3">
          <button className="btn-secondary">
            <BarChart3 className="w-4 h-4 mr-2" />
            生成报告
          </button>
          <button className="btn-primary">
            <TrendingUp className="w-4 h-4 mr-2" />
            查看分析
          </button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <div key={index} className="card">
            <div className="card-body">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">{stat.title}</p>
                  <p className="text-2xl font-bold text-white mt-1">{stat.value}</p>
                  <div className="flex items-center mt-2">
                    <span className={`text-sm ${
                      stat.changeType === 'increase' ? 'text-green-400' :
                      stat.changeType === 'decrease' ? 'text-red-400' : 'text-gray-400'
                    }`}>
                      {stat.change}
                    </span>
                    <span className="text-xs text-gray-400 ml-1">本月</span>
                  </div>
                </div>
                <div className={`p-3 rounded-lg bg-gray-700 ${stat.color}`}>
                  <stat.icon className="w-6 h-6" />
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 最近项目 */}
        <div className="lg:col-span-2 card">
          <div className="card-header">
            <h3 className="text-lg font-semibold text-white">最近项目</h3>
            <button className="text-sm text-primary-400 hover:text-primary-300">
              查看全部
            </button>
          </div>
          <div className="card-body">
            <div className="space-y-4">
              {recentProjects.map((project) => (
                <div key={project.id} className="flex items-center justify-between p-4 bg-dark-700 rounded-lg">
                  <div className="flex-1">
                    <h4 className="font-medium text-white">{project.name}</h4>
                    <div className="flex items-center space-x-4 mt-2">
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        project.status === '进行中' ? 'bg-blue-500/20 text-blue-400' :
                        project.status === '审核中' ? 'bg-yellow-500/20 text-yellow-400' :
                        'bg-green-500/20 text-green-400'
                      }`}>
                        {project.status}
                      </span>
                      <span className="text-xs text-gray-400">
                        <Clock className="w-3 h-3 inline mr-1" />
                        {project.lastModified}
                      </span>
                    </div>
                    <div className="mt-3">
                      <div className="flex items-center justify-between text-xs text-gray-400 mb-1">
                        <span>进度</span>
                        <span>{project.progress}%</span>
                      </div>
                      <div className="w-full bg-gray-600 rounded-full h-2">
                        <div 
                          className="bg-primary-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${project.progress}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                  <div className="ml-4 flex -space-x-2">
                    {project.team.map((member, idx) => (
                      <div 
                        key={idx}
                        className="w-8 h-8 bg-gradient-to-br from-primary-400 to-primary-600 rounded-full flex items-center justify-center text-white text-xs font-medium border-2 border-dark-700"
                        title={member}
                      >
                        {member.charAt(0)}
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 系统通知 */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold text-white">系统通知</h3>
            <span className="text-xs text-gray-400">{alerts.length} 条新消息</span>
          </div>
          <div className="card-body">
            <div className="space-y-3">
              {alerts.map((alert, index) => (
                <div key={index} className="flex items-start space-x-3 p-3 bg-dark-700 rounded-lg">
                  <div className={`p-1 rounded-full ${
                    alert.type === 'warning' ? 'bg-yellow-500/20' :
                    alert.type === 'info' ? 'bg-blue-500/20' : 'bg-green-500/20'
                  }`}>
                    <AlertTriangle className={`w-4 h-4 ${
                      alert.type === 'warning' ? 'text-yellow-400' :
                      alert.type === 'info' ? 'text-blue-400' : 'text-green-400'
                    }`} />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-gray-200">{alert.message}</p>
                    <p className="text-xs text-gray-400 mt-1">{alert.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Dashboard
