/**
 * Cesium服务 - 封装Cesium相关操作
 */

import { Coordinate, Road, TerrainData } from '../types'

declare global {
  interface Window {
    Cesium: any
  }
}

export class CesiumService {
  private viewer: any
  private Cesium: any

  constructor(viewer: any) {
    this.viewer = viewer
    this.Cesium = window.Cesium
  }

  /**
   * 添加道路到场景
   */
  addRoadToScene(road: Road): any {
    if (!road.coordinates || road.coordinates.length < 2) {
      console.warn('道路坐标点不足，无法显示')
      return null
    }

    const positions = road.coordinates.map(coord => 
      this.Cesium.Cartesian3.fromDegrees(
        coord.longitude, 
        coord.latitude, 
        coord.elevation || 0
      )
    )

    // 创建道路中心线
    const roadEntity = this.viewer.entities.add({
      id: `road-${road.id}`,
      name: road.name,
      polyline: {
        positions: positions,
        width: road.width,
        material: this.getRoadMaterial(road.surfaceType),
        clampToGround: true,
        outline: true,
        outlineColor: this.Cesium.Color.BLACK,
        outlineWidth: 2
      },
      properties: {
        type: 'road',
        roadId: road.id,
        roadData: road
      }
    })

    // 添加道路边界
    this.addRoadBoundary(road, positions)

    // 添加道路标注
    this.addRoadLabels(road, positions)

    return roadEntity
  }

  /**
   * 获取道路材质
   */
  private getRoadMaterial(surfaceType: string): any {
    const materials = {
      asphalt: this.Cesium.Color.DARKSLATEGRAY,
      concrete: this.Cesium.Color.LIGHTGRAY,
      gravel: this.Cesium.Color.SANDYBROWN,
      dirt: this.Cesium.Color.SADDLEBROWN
    }

    return materials[surfaceType as keyof typeof materials] || this.Cesium.Color.GRAY
  }

  /**
   * 添加道路边界
   */
  private addRoadBoundary(road: Road, centerPositions: any[]): void {
    const halfWidth = road.width / 2

    // 计算左右边界点
    const leftPositions: any[] = []
    const rightPositions: any[] = []

    for (let i = 0; i < centerPositions.length; i++) {
      const position = centerPositions[i]
      const cartographic = this.Cesium.Cartographic.fromCartesian(position)
      
      // 计算垂直方向（简化处理）
      let bearing = 0
      if (i < centerPositions.length - 1) {
        const nextPosition = centerPositions[i + 1]
        const nextCartographic = this.Cesium.Cartographic.fromCartesian(nextPosition)
        bearing = this.calculateBearing(cartographic, nextCartographic)
      } else if (i > 0) {
        const prevPosition = centerPositions[i - 1]
        const prevCartographic = this.Cesium.Cartographic.fromCartesian(prevPosition)
        bearing = this.calculateBearing(prevCartographic, cartographic)
      }

      // 计算左右偏移点
      const leftBearing = bearing + Math.PI / 2
      const rightBearing = bearing - Math.PI / 2

      const leftPoint = this.offsetPosition(cartographic, leftBearing, halfWidth)
      const rightPoint = this.offsetPosition(cartographic, rightBearing, halfWidth)

      leftPositions.push(this.Cesium.Cartesian3.fromRadians(
        leftPoint.longitude, leftPoint.latitude, leftPoint.height
      ))
      rightPositions.push(this.Cesium.Cartesian3.fromRadians(
        rightPoint.longitude, rightPoint.latitude, rightPoint.height
      ))
    }

    // 添加左边界线
    this.viewer.entities.add({
      id: `road-${road.id}-left-boundary`,
      polyline: {
        positions: leftPositions,
        width: 1,
        material: this.Cesium.Color.WHITE.withAlpha(0.8),
        clampToGround: true
      }
    })

    // 添加右边界线
    this.viewer.entities.add({
      id: `road-${road.id}-right-boundary`,
      polyline: {
        positions: rightPositions,
        width: 1,
        material: this.Cesium.Color.WHITE.withAlpha(0.8),
        clampToGround: true
      }
    })
  }

  /**
   * 计算方位角
   */
  private calculateBearing(from: any, to: any): number {
    const deltaLon = to.longitude - from.longitude
    const y = Math.sin(deltaLon) * Math.cos(to.latitude)
    const x = Math.cos(from.latitude) * Math.sin(to.latitude) - 
              Math.sin(from.latitude) * Math.cos(to.latitude) * Math.cos(deltaLon)
    return Math.atan2(y, x)
  }

  /**
   * 偏移位置
   */
  private offsetPosition(position: any, bearing: number, distance: number): any {
    const R = 6371000 // 地球半径（米）
    const lat1 = position.latitude
    const lon1 = position.longitude

    const lat2 = Math.asin(
      Math.sin(lat1) * Math.cos(distance / R) +
      Math.cos(lat1) * Math.sin(distance / R) * Math.cos(bearing)
    )

    const lon2 = lon1 + Math.atan2(
      Math.sin(bearing) * Math.sin(distance / R) * Math.cos(lat1),
      Math.cos(distance / R) - Math.sin(lat1) * Math.sin(lat2)
    )

    return {
      longitude: lon2,
      latitude: lat2,
      height: position.height
    }
  }

  /**
   * 添加道路标注
   */
  private addRoadLabels(road: Road, positions: any[]): void {
    if (positions.length === 0) return

    // 在道路中点添加标注
    const midIndex = Math.floor(positions.length / 2)
    const midPosition = positions[midIndex]

    this.viewer.entities.add({
      id: `road-${road.id}-label`,
      position: midPosition,
      label: {
        text: road.name,
        font: '14pt sans-serif',
        fillColor: this.Cesium.Color.WHITE,
        outlineColor: this.Cesium.Color.BLACK,
        outlineWidth: 2,
        style: this.Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new this.Cesium.Cartesian2(0, -50),
        heightReference: this.Cesium.HeightReference.CLAMP_TO_GROUND
      }
    })
  }

  /**
   * 移除道路
   */
  removeRoad(roadId: number): void {
    const entities = this.viewer.entities.values
    const toRemove = entities.filter((entity: any) => 
      entity.id.includes(`road-${roadId}`)
    )

    toRemove.forEach((entity: any) => {
      this.viewer.entities.remove(entity)
    })
  }

  /**
   * 高亮道路
   */
  highlightRoad(roadId: number, highlight: boolean = true): void {
    const roadEntity = this.viewer.entities.getById(`road-${roadId}`)
    if (roadEntity && roadEntity.polyline) {
      if (highlight) {
        roadEntity.polyline.material = this.Cesium.Color.YELLOW
        roadEntity.polyline.width = roadEntity.polyline.width._value * 1.5
      } else {
        // 恢复原始材质和宽度
        const roadData = roadEntity.properties.roadData._value
        roadEntity.polyline.material = this.getRoadMaterial(roadData.surfaceType)
        roadEntity.polyline.width = roadData.width
      }
    }
  }

  /**
   * 飞行到道路
   */
  flyToRoad(road: Road): void {
    if (!road.coordinates || road.coordinates.length === 0) return

    const positions = road.coordinates.map(coord => 
      this.Cesium.Cartesian3.fromDegrees(coord.longitude, coord.latitude, coord.elevation || 0)
    )

    // 计算边界球
    const boundingSphere = this.Cesium.BoundingSphere.fromPoints(positions)
    
    this.viewer.camera.flyToBoundingSphere(boundingSphere, {
      duration: 2.0,
      offset: new this.Cesium.HeadingPitchRange(0, -0.5, boundingSphere.radius * 2)
    })
  }

  /**
   * 添加地形数据
   */
  addTerrainData(terrainData: TerrainData): void {
    // 根据地形数据类型添加到场景
    switch (terrainData.type) {
      case 'dem':
        this.addDEMTerrain(terrainData)
        break
      case 'contour':
        this.addContourLines(terrainData)
        break
      case 'point-cloud':
        this.addPointCloud(terrainData)
        break
    }
  }

  /**
   * 添加DEM地形
   */
  private addDEMTerrain(terrainData: TerrainData): void {
    // 创建自定义地形提供者
    const terrainProvider = new this.Cesium.CesiumTerrainProvider({
      url: terrainData.filePath
    })

    this.viewer.terrainProvider = terrainProvider
  }

  /**
   * 添加等高线
   */
  private addContourLines(terrainData: TerrainData): void {
    // 实现等高线显示逻辑
    console.log('添加等高线:', terrainData)
  }

  /**
   * 添加点云数据
   */
  private addPointCloud(terrainData: TerrainData): void {
    // 实现点云显示逻辑
    console.log('添加点云数据:', terrainData)
  }

  /**
   * 测量距离
   */
  measureDistance(startCoord: Coordinate, endCoord: Coordinate): number {
    const start = this.Cesium.Cartesian3.fromDegrees(
      startCoord.longitude, startCoord.latitude, startCoord.elevation || 0
    )
    const end = this.Cesium.Cartesian3.fromDegrees(
      endCoord.longitude, endCoord.latitude, endCoord.elevation || 0
    )

    return this.Cesium.Cartesian3.distance(start, end)
  }

  /**
   * 测量面积
   */
  measureArea(coordinates: Coordinate[]): number {
    if (coordinates.length < 3) return 0

    const positions = coordinates.map(coord => 
      this.Cesium.Cartesian3.fromDegrees(coord.longitude, coord.latitude, coord.elevation || 0)
    )

    // 使用三角剖分计算面积
    const triangles = this.Cesium.PolygonPipeline.triangulate(positions)
    let totalArea = 0

    for (let i = 0; i < triangles.length; i += 3) {
      const p1 = positions[triangles[i]]
      const p2 = positions[triangles[i + 1]]
      const p3 = positions[triangles[i + 2]]

      const area = this.calculateTriangleArea(p1, p2, p3)
      totalArea += area
    }

    return totalArea
  }

  /**
   * 计算三角形面积
   */
  private calculateTriangleArea(p1: any, p2: any, p3: any): number {
    const v1 = this.Cesium.Cartesian3.subtract(p2, p1, new this.Cesium.Cartesian3())
    const v2 = this.Cesium.Cartesian3.subtract(p3, p1, new this.Cesium.Cartesian3())
    const cross = this.Cesium.Cartesian3.cross(v1, v2, new this.Cesium.Cartesian3())
    return this.Cesium.Cartesian3.magnitude(cross) / 2
  }

  /**
   * 设置视角
   */
  setView(longitude: number, latitude: number, height: number, 
          heading: number = 0, pitch: number = -45, roll: number = 0): void {
    this.viewer.camera.setView({
      destination: this.Cesium.Cartesian3.fromDegrees(longitude, latitude, height),
      orientation: {
        heading: this.Cesium.Math.toRadians(heading),
        pitch: this.Cesium.Math.toRadians(pitch),
        roll: this.Cesium.Math.toRadians(roll)
      }
    })
  }

  /**
   * 截图
   */
  takeScreenshot(): Promise<string> {
    return new Promise((resolve) => {
      this.viewer.render()
      const canvas = this.viewer.scene.canvas
      resolve(canvas.toDataURL('image/png'))
    })
  }

  /**
   * 清除所有实体
   */
  clearAll(): void {
    this.viewer.entities.removeAll()
  }

  /**
   * 销毁
   */
  destroy(): void {
    if (this.viewer) {
      this.viewer.destroy()
    }
  }
}
