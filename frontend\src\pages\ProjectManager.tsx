import React from 'react'
import {
  Plus,
  Search,
  Filter,
  MoreVertical,
  FolderOpen,
  Calendar,
  Users,
  MapPin
} from 'lucide-react'

const ProjectManager: React.FC = () => {
  const projects = [
    {
      id: 1,
      name: '矿山A区主干道设计',
      description: '连接矿区主要作业面的主干道路设计项目',
      status: '进行中',
      progress: 75,
      createdAt: '2024-01-15',
      lastModified: '2小时前',
      team: ['张工程师', '李工程师', '王工程师'],
      location: '内蒙古包头市',
      roads: 8,
      totalLength: '12.5 km'
    },
    {
      id: 2,
      name: 'B区运输道路优化',
      description: '优化现有运输道路，提高运输效率',
      status: '审核中',
      progress: 90,
      createdAt: '2024-01-10',
      lastModified: '1天前',
      team: ['陈工程师', '刘工程师'],
      location: '山西大同市',
      roads: 5,
      totalLength: '8.2 km'
    },
    {
      id: 3,
      name: 'C区新建道路规划',
      description: '新开采区域的道路网络规划设计',
      status: '设计中',
      progress: 45,
      createdAt: '2024-01-20',
      lastModified: '3天前',
      team: ['赵工程师', '孙工程师', '周工程师'],
      location: '陕西榆林市',
      roads: 12,
      totalLength: '18.7 km'
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case '进行中':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30'
      case '审核中':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      case '设计中':
        return 'bg-green-500/20 text-green-400 border-green-500/30'
      case '已完成':
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  return (
    <div className="p-6 h-full overflow-y-auto">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-white">项目管理</h1>
          <p className="text-gray-400 mt-1">管理和组织您的道路设计项目</p>
        </div>
        <button className="btn-primary">
          <Plus className="w-4 h-4 mr-2" />
          新建项目
        </button>
      </div>

      {/* 搜索和筛选 */}
      <div className="flex items-center space-x-4 mb-6">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="搜索项目名称、位置或团队成员..."
            className="pl-10 input"
          />
        </div>
        <button className="btn-secondary">
          <Filter className="w-4 h-4 mr-2" />
          筛选
        </button>
      </div>

      {/* 项目统计 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="card">
          <div className="card-body">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">总项目数</p>
                <p className="text-2xl font-bold text-white">{projects.length}</p>
              </div>
              <FolderOpen className="w-8 h-8 text-blue-400" />
            </div>
          </div>
        </div>
        
        <div className="card">
          <div className="card-body">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">进行中</p>
                <p className="text-2xl font-bold text-white">
                  {projects.filter(p => p.status === '进行中').length}
                </p>
              </div>
              <div className="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center">
                <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="card">
          <div className="card-body">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">总道路数</p>
                <p className="text-2xl font-bold text-white">
                  {projects.reduce((sum, p) => sum + p.roads, 0)}
                </p>
              </div>
              <MapPin className="w-8 h-8 text-green-400" />
            </div>
          </div>
        </div>
        
        <div className="card">
          <div className="card-body">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">总长度</p>
                <p className="text-2xl font-bold text-white">
                  {projects.reduce((sum, p) => sum + parseFloat(p.totalLength), 0).toFixed(1)} km
                </p>
              </div>
              <div className="w-8 h-8 bg-primary-500/20 rounded-lg flex items-center justify-center">
                <div className="w-4 h-1 bg-primary-500 rounded-full"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 项目列表 */}
      <div className="space-y-4">
        {projects.map((project) => (
          <div key={project.id} className="card hover:border-gray-600 transition-colors cursor-pointer">
            <div className="card-body">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-lg font-semibold text-white">{project.name}</h3>
                    <span className={`px-2 py-1 text-xs rounded-full border ${getStatusColor(project.status)}`}>
                      {project.status}
                    </span>
                  </div>
                  
                  <p className="text-gray-400 text-sm mb-4">{project.description}</p>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    <div className="flex items-center space-x-2 text-sm text-gray-300">
                      <MapPin className="w-4 h-4 text-gray-400" />
                      <span>{project.location}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-300">
                      <Calendar className="w-4 h-4 text-gray-400" />
                      <span>{project.lastModified}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-300">
                      <Users className="w-4 h-4 text-gray-400" />
                      <span>{project.team.length} 成员</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-300">
                      <span>{project.roads} 条道路</span>
                      <span>•</span>
                      <span>{project.totalLength}</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="text-sm text-gray-400">进度:</div>
                      <div className="flex items-center space-x-2">
                        <div className="w-32 bg-gray-600 rounded-full h-2">
                          <div 
                            className="bg-primary-500 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${project.progress}%` }}
                          ></div>
                        </div>
                        <span className="text-sm text-gray-300">{project.progress}%</span>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <div className="flex -space-x-2">
                        {project.team.slice(0, 3).map((member, idx) => (
                          <div 
                            key={idx}
                            className="w-8 h-8 bg-gradient-to-br from-primary-400 to-primary-600 rounded-full flex items-center justify-center text-white text-xs font-medium border-2 border-dark-800"
                            title={member}
                          >
                            {member.charAt(0)}
                          </div>
                        ))}
                        {project.team.length > 3 && (
                          <div className="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center text-white text-xs font-medium border-2 border-dark-800">
                            +{project.team.length - 3}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
                
                <button className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
                  <MoreVertical className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default ProjectManager
