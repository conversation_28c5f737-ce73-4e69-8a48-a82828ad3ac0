"""
道路设计核心算法服务
"""

import math
import numpy as np
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass

from app.models.road import Road, RoadType, SurfaceType


@dataclass
class Point3D:
    """三维点"""
    x: float
    y: float
    z: float


@dataclass
class RoadSegment:
    """道路段"""
    start_point: Point3D
    end_point: Point3D
    length: float
    grade: float
    radius: Optional[float] = None


@dataclass
class DesignConstraints:
    """设计约束条件"""
    max_grade: float = 8.0  # 最大坡度(%)
    min_radius: float = 25.0  # 最小转弯半径(m)
    max_speed: float = 30.0  # 最大设计速度(km/h)
    road_width: float = 6.0  # 道路宽度(m)
    safety_factor: float = 1.2  # 安全系数


class RoadDesignService:
    """道路设计服务"""
    
    @staticmethod
    def calculate_distance(p1: Point3D, p2: Point3D) -> float:
        """计算两点间距离"""
        return math.sqrt((p2.x - p1.x)**2 + (p2.y - p1.y)**2 + (p2.z - p1.z)**2)
    
    @staticmethod
    def calculate_grade(p1: Point3D, p2: Point3D) -> float:
        """计算坡度(%)"""
        horizontal_distance = math.sqrt((p2.x - p1.x)**2 + (p2.y - p1.y)**2)
        if horizontal_distance == 0:
            return 0.0
        vertical_distance = p2.z - p1.z
        return (vertical_distance / horizontal_distance) * 100
    
    @staticmethod
    def calculate_curve_radius(p1: Point3D, p2: Point3D, p3: Point3D) -> float:
        """计算曲线半径"""
        # 使用三点计算圆弧半径
        a = RoadDesignService.calculate_distance(p1, p2)
        b = RoadDesignService.calculate_distance(p2, p3)
        c = RoadDesignService.calculate_distance(p1, p3)
        
        # 海伦公式计算面积
        s = (a + b + c) / 2
        area = math.sqrt(s * (s - a) * (s - b) * (s - c))
        
        if area == 0:
            return float('inf')
        
        # 外接圆半径
        radius = (a * b * c) / (4 * area)
        return radius
    
    @classmethod
    def analyze_road_geometry(cls, coordinates: List[Dict]) -> Dict:
        """分析道路几何特性"""
        if len(coordinates) < 2:
            return {"error": "至少需要2个坐标点"}
        
        points = [Point3D(coord['longitude'], coord['latitude'], coord.get('elevation', 0)) 
                 for coord in coordinates]
        
        segments = []
        total_length = 0.0
        grades = []
        radii = []
        
        # 分析每个路段
        for i in range(len(points) - 1):
            segment_length = cls.calculate_distance(points[i], points[i + 1])
            segment_grade = cls.calculate_grade(points[i], points[i + 1])
            
            segment = RoadSegment(
                start_point=points[i],
                end_point=points[i + 1],
                length=segment_length,
                grade=segment_grade
            )
            
            segments.append(segment)
            total_length += segment_length
            grades.append(abs(segment_grade))
            
            # 计算转弯半径（需要三个点）
            if i < len(points) - 2:
                radius = cls.calculate_curve_radius(points[i], points[i + 1], points[i + 2])
                radii.append(radius)
        
        return {
            "total_length": total_length,
            "segment_count": len(segments),
            "grades": {
                "min": min(grades) if grades else 0,
                "max": max(grades) if grades else 0,
                "average": sum(grades) / len(grades) if grades else 0
            },
            "radii": {
                "min": min(radii) if radii else float('inf'),
                "max": max(radii) if radii else 0,
                "average": sum(radii) / len(radii) if radii else 0
            },
            "elevation_change": points[-1].z - points[0].z if len(points) >= 2 else 0
        }
    
    @classmethod
    def validate_design(cls, coordinates: List[Dict], constraints: DesignConstraints) -> Dict:
        """验证道路设计是否符合约束条件"""
        analysis = cls.analyze_road_geometry(coordinates)
        
        if "error" in analysis:
            return analysis
        
        violations = []
        warnings = []
        
        # 检查坡度约束
        if analysis["grades"]["max"] > constraints.max_grade:
            violations.append(f"最大坡度 {analysis['grades']['max']:.1f}% 超过限制 {constraints.max_grade}%")
        
        if analysis["grades"]["max"] > constraints.max_grade * 0.8:
            warnings.append(f"坡度 {analysis['grades']['max']:.1f}% 接近限制值")
        
        # 检查转弯半径约束
        if analysis["radii"]["min"] < constraints.min_radius:
            violations.append(f"最小转弯半径 {analysis['radii']['min']:.1f}m 小于限制 {constraints.min_radius}m")
        
        if analysis["radii"]["min"] < constraints.min_radius * 1.2:
            warnings.append(f"转弯半径 {analysis['radii']['min']:.1f}m 接近限制值")
        
        return {
            "valid": len(violations) == 0,
            "violations": violations,
            "warnings": warnings,
            "analysis": analysis
        }
    
    @classmethod
    def optimize_road_path(cls, start: Point3D, end: Point3D, terrain_data: Optional[np.ndarray] = None,
                          constraints: DesignConstraints = None) -> List[Point3D]:
        """优化道路路径"""
        if constraints is None:
            constraints = DesignConstraints()
        
        # 简单的直线路径（实际应用中需要考虑地形和约束）
        # 这里实现一个基础的路径优化算法
        
        # 计算直线距离和方向
        dx = end.x - start.x
        dy = end.y - start.y
        dz = end.z - start.z
        
        horizontal_distance = math.sqrt(dx**2 + dy**2)
        
        # 如果坡度过大，需要增加中间点
        direct_grade = abs(dz / horizontal_distance * 100) if horizontal_distance > 0 else 0
        
        if direct_grade <= constraints.max_grade:
            # 直线路径可行
            return [start, end]
        
        # 需要增加中间点来降低坡度
        required_horizontal_distance = abs(dz) / (constraints.max_grade / 100)
        segments_needed = math.ceil(required_horizontal_distance / horizontal_distance)
        
        optimized_path = [start]
        
        for i in range(1, segments_needed):
            t = i / segments_needed
            intermediate_point = Point3D(
                start.x + t * dx,
                start.y + t * dy,
                start.z + t * dz
            )
            optimized_path.append(intermediate_point)
        
        optimized_path.append(end)
        
        return optimized_path
    
    @classmethod
    def calculate_earthwork_volume(cls, coordinates: List[Dict], road_width: float = 6.0,
                                 cut_slope: float = 1.5, fill_slope: float = 2.0) -> Dict:
        """计算土方工程量"""
        if len(coordinates) < 2:
            return {"error": "至少需要2个坐标点"}
        
        total_cut_volume = 0.0
        total_fill_volume = 0.0
        
        # 简化的土方计算（实际需要详细的横断面设计）
        for i in range(len(coordinates) - 1):
            segment_length = cls.calculate_distance(
                Point3D(coordinates[i]['longitude'], coordinates[i]['latitude'], coordinates[i].get('elevation', 0)),
                Point3D(coordinates[i+1]['longitude'], coordinates[i+1]['latitude'], coordinates[i+1].get('elevation', 0))
            )
            
            # 假设平均挖填深度（实际需要根据地形数据计算）
            avg_cut_depth = 2.0  # 平均挖方深度
            avg_fill_depth = 1.5  # 平均填方深度
            
            # 计算横断面积
            cut_area = road_width * avg_cut_depth + 0.5 * (avg_cut_depth**2) * cut_slope
            fill_area = road_width * avg_fill_depth + 0.5 * (avg_fill_depth**2) * fill_slope
            
            # 计算体积
            segment_cut_volume = cut_area * segment_length
            segment_fill_volume = fill_area * segment_length
            
            total_cut_volume += segment_cut_volume
            total_fill_volume += segment_fill_volume
        
        return {
            "cut_volume": total_cut_volume,
            "fill_volume": total_fill_volume,
            "net_volume": total_cut_volume - total_fill_volume,
            "balance_ratio": total_fill_volume / total_cut_volume if total_cut_volume > 0 else 0
        }
    
    @classmethod
    def estimate_construction_cost(cls, road: Road, earthwork: Dict) -> Dict:
        """估算施工成本"""
        # 基础单价（元/立方米或元/平方米）
        unit_costs = {
            "earthwork_cut": 15.0,  # 挖方单价
            "earthwork_fill": 12.0,  # 填方单价
            "pavement": {
                SurfaceType.DIRT: 0,
                SurfaceType.GRAVEL: 80,
                SurfaceType.ASPHALT: 200,
                SurfaceType.CONCRETE: 300
            },
            "drainage": 50.0,  # 排水设施单价
            "safety": 30.0     # 安全设施单价
        }
        
        # 计算各项成本
        earthwork_cost = (
            earthwork.get("cut_volume", 0) * unit_costs["earthwork_cut"] +
            earthwork.get("fill_volume", 0) * unit_costs["earthwork_fill"]
        )
        
        pavement_area = road.length * road.width
        pavement_cost = pavement_area * unit_costs["pavement"].get(road.surface_type, 0)
        
        drainage_cost = road.length * unit_costs["drainage"]
        safety_cost = road.length * unit_costs["safety"]
        
        # 管理费和利润（总成本的20%）
        subtotal = earthwork_cost + pavement_cost + drainage_cost + safety_cost
        management_cost = subtotal * 0.2
        
        total_cost = subtotal + management_cost
        
        return {
            "earthwork_cost": earthwork_cost,
            "pavement_cost": pavement_cost,
            "drainage_cost": drainage_cost,
            "safety_cost": safety_cost,
            "management_cost": management_cost,
            "total_cost": total_cost,
            "cost_per_meter": total_cost / road.length if road.length > 0 else 0
        }
