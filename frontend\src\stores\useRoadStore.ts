import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { Road, RoadDesignParams, Coordinate } from '../types'

interface RoadState {
  // 道路数据
  roads: Road[]
  currentRoad: Road | null
  
  // 设计参数
  designParams: RoadDesignParams
  
  // 绘制状态
  isDrawing: boolean
  drawingCoordinates: Coordinate[]
  selectedTool: string
  
  // 分析结果
  analysisResult: any
  
  // 操作方法
  setRoads: (roads: Road[]) => void
  setCurrentRoad: (road: Road | null) => void
  updateDesignParams: (params: Partial<RoadDesignParams>) => void
  startDrawing: () => void
  stopDrawing: () => void
  addCoordinate: (coordinate: Coordinate) => void
  clearCoordinates: () => void
  setSelectedTool: (tool: string) => void
  setAnalysisResult: (result: any) => void
  addRoad: (road: Road) => void
  updateRoad: (id: number, updates: Partial<Road>) => void
  removeRoad: (id: number) => void
}

const defaultDesignParams: RoadDesignParams = {
  width: 6,
  maxGrade: 8,
  minRadius: 25,
  surfaceType: 'asphalt',
  designSpeed: 30,
  loadCapacity: 50,
  drainageType: 'surface',
  shoulderWidth: 1
}

export const useRoadStore = create<RoadState>()(
  devtools(
    (set, get) => ({
      // 初始状态
      roads: [],
      currentRoad: null,
      designParams: defaultDesignParams,
      isDrawing: false,
      drawingCoordinates: [],
      selectedTool: 'pointer',
      analysisResult: null,

      // 操作方法
      setRoads: (roads) => set({ roads }),

      setCurrentRoad: (road) => set({ currentRoad: road }),

      updateDesignParams: (params) => set((state) => ({
        designParams: { ...state.designParams, ...params }
      })),

      startDrawing: () => set({
        isDrawing: true,
        drawingCoordinates: []
      }),

      stopDrawing: () => set({
        isDrawing: false
      }),

      addCoordinate: (coordinate) => set((state) => ({
        drawingCoordinates: [...state.drawingCoordinates, coordinate]
      })),

      clearCoordinates: () => set({
        drawingCoordinates: []
      }),

      setSelectedTool: (tool) => set({ selectedTool: tool }),

      setAnalysisResult: (result) => set({ analysisResult: result }),

      addRoad: (road) => set((state) => ({
        roads: [...state.roads, road]
      })),

      updateRoad: (id, updates) => set((state) => ({
        roads: state.roads.map(road => 
          road.id === id ? { ...road, ...updates } : road
        ),
        currentRoad: state.currentRoad?.id === id 
          ? { ...state.currentRoad, ...updates } 
          : state.currentRoad
      })),

      removeRoad: (id) => set((state) => ({
        roads: state.roads.filter(road => road.id !== id),
        currentRoad: state.currentRoad?.id === id ? null : state.currentRoad
      }))
    }),
    {
      name: 'road-store'
    }
  )
)
