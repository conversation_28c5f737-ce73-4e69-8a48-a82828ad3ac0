# 安装指南

## 🎯 演示页面

我已经为您创建了一个演示页面，展示了系统的界面设计和基本功能：

**演示页面**: `demo.html` - 已在浏览器中打开

这个演示页面包含：
- ✅ 完整的用户界面设计
- ✅ Cesium 3D地球引擎集成
- ✅ 交互式工具栏
- ✅ 道路参数设置面板
- ✅ 实时分析结果显示
- ✅ 示例道路渲染

## 🚀 完整开发环境设置

要运行完整的前后端应用，请按以下步骤操作：

### 1. 安装必要软件

#### Node.js (前端开发)
```bash
# 下载并安装 Node.js 18+ 
# 访问: https://nodejs.org/
# 验证安装
node --version
npm --version
```

#### Python (后端开发)
```bash
# 下载并安装 Python 3.11+
# 访问: https://python.org/
# 验证安装
python --version
pip --version
```

#### PostgreSQL (数据库)
```bash
# 下载并安装 PostgreSQL 15+
# 访问: https://postgresql.org/
# 创建数据库
createdb openpit_road_design
```

#### Redis (缓存)
```bash
# 下载并安装 Redis
# 访问: https://redis.io/
```

### 2. 前端设置

```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 访问: http://localhost:5173
```

### 3. 后端设置

```bash
# 进入后端目录
cd backend

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 运行数据库迁移
alembic upgrade head

# 启动开发服务器
uvicorn app.main:app --reload

# 访问: http://localhost:8000
```

### 4. 环境配置

编辑 `.env` 文件：

```bash
# 数据库配置
POSTGRES_SERVER=localhost
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_password
POSTGRES_DB=openpit_road_design

# Cesium配置 (重要!)
CESIUM_ION_ACCESS_TOKEN=your_cesium_ion_token

# 其他配置...
```

**获取Cesium Ion令牌**:
1. 访问 https://cesium.com/ion/
2. 注册免费账户
3. 获取访问令牌
4. 在 `.env` 文件中配置

### 5. Docker部署 (推荐)

如果您有Docker，可以使用容器化部署：

```bash
# 确保Docker已安装
docker --version
docker-compose --version

# 启动所有服务
docker-compose up -d

# 访问应用
# 前端: http://localhost
# 后端API: http://localhost:8000
```

## 🔧 故障排除

### 常见问题

1. **Cesium无法加载**
   - 确保配置了有效的Cesium Ion访问令牌
   - 检查网络连接

2. **数据库连接失败**
   - 确保PostgreSQL服务正在运行
   - 检查数据库连接配置

3. **前端依赖安装失败**
   - 清除npm缓存: `npm cache clean --force`
   - 删除node_modules重新安装

4. **后端依赖安装失败**
   - 确保Python版本正确
   - 使用虚拟环境

### 系统要求

- **操作系统**: Windows 10+, macOS 10.15+, Ubuntu 18.04+
- **内存**: 最少4GB，推荐8GB+
- **存储**: 最少2GB可用空间
- **网络**: 稳定的互联网连接（用于Cesium地形数据）

## 📱 功能特性

### 已实现功能
- ✅ 三维地球可视化
- ✅ 道路绘制工具
- ✅ 参数化设计
- ✅ 实时分析
- ✅ 项目管理
- ✅ 数据导入导出

### 核心组件
- **CesiumViewer**: 三维地球引擎
- **RoadDesignTools**: 道路设计工具
- **RoadAnalysisPanel**: 分析结果面板
- **InteractiveToolbar**: 交互式工具栏
- **RealTimePreview**: 实时预览

## 🎮 使用说明

### 基本操作
1. **选择工具**: 点击工具栏中的选择工具
2. **绘制道路**: 选择绘制工具，在地图上点击绘制路径
3. **调整参数**: 在左侧面板调整道路宽度、坡度等参数
4. **查看分析**: 右侧面板显示实时分析结果
5. **保存项目**: 使用Ctrl+S保存当前设计

### 快捷键
- `V` - 选择工具
- `D` - 绘制工具
- `M` - 移动工具
- `R` - 测量工具
- `Ctrl+S` - 保存
- `Ctrl+Z` - 撤销
- `F11` - 全屏

## 📞 技术支持

如果您在安装或使用过程中遇到问题：

1. 查看控制台错误信息
2. 检查网络连接
3. 确认所有依赖已正确安装
4. 参考项目文档

## 🎉 开始使用

现在您可以：
1. 查看演示页面了解界面设计
2. 按照安装指南设置开发环境
3. 开始您的道路设计项目！

祝您使用愉快！🚀
