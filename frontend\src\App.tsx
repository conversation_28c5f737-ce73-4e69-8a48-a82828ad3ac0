import React from 'react'
import { Routes, Route } from 'react-router-dom'

import Layout from './components/Layout'
import Dashboard from './pages/Dashboard'
import ProjectManager from './pages/ProjectManager'
import RoadDesigner from './pages/RoadDesigner'
import TerrainViewer from './pages/TerrainViewer'
import Settings from './pages/Settings'

function App() {
  return (
    <div className="h-screen w-screen overflow-hidden bg-dark-900">
      <Routes>
        <Route path="/" element={<Layout />}>
          <Route index element={<Dashboard />} />
          <Route path="projects" element={<ProjectManager />} />
          <Route path="design" element={<RoadDesigner />} />
          <Route path="terrain" element={<TerrainViewer />} />
          <Route path="settings" element={<Settings />} />
        </Route>
      </Routes>
    </div>
  )
}

export default App
