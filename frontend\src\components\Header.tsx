import React from 'react'
import {
  Bell,
  User,
  Search,
  Maximize2,
  Minimize2,
  RefreshCw,
  Download,
  Share2
} from 'lucide-react'

const Header: React.FC = () => {
  const [isFullscreen, setIsFullscreen] = React.useState(false)

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen()
      setIsFullscreen(true)
    } else {
      document.exitFullscreen()
      setIsFullscreen(false)
    }
  }

  return (
    <header className="h-16 bg-dark-800 border-b border-gray-700 flex items-center justify-between px-6">
      {/* 左侧：搜索和面包屑 */}
      <div className="flex items-center space-x-4 flex-1">
        {/* 搜索框 */}
        <div className="relative max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="搜索项目、道路或工具..."
            className="pl-10 pr-4 py-2 w-80 bg-dark-700 border border-gray-600 rounded-lg text-sm text-gray-100 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
        </div>

        {/* 面包屑导航 */}
        <nav className="flex items-center space-x-2 text-sm text-gray-400">
          <span>工作区</span>
          <span>/</span>
          <span className="text-gray-200">当前项目</span>
        </nav>
      </div>

      {/* 中间：项目信息 */}
      <div className="flex items-center space-x-4">
        <div className="text-center">
          <div className="text-sm font-medium text-gray-200">矿山A区道路设计</div>
          <div className="text-xs text-gray-400">最后保存: 2分钟前</div>
        </div>
      </div>

      {/* 右侧：操作按钮和用户信息 */}
      <div className="flex items-center space-x-3">
        {/* 操作按钮 */}
        <div className="flex items-center space-x-2">
          <button
            className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
            title="刷新数据"
          >
            <RefreshCw className="w-4 h-4" />
          </button>
          
          <button
            className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
            title="导出项目"
          >
            <Download className="w-4 h-4" />
          </button>
          
          <button
            className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
            title="分享项目"
          >
            <Share2 className="w-4 h-4" />
          </button>
          
          <button
            onClick={toggleFullscreen}
            className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
            title={isFullscreen ? "退出全屏" : "全屏显示"}
          >
            {isFullscreen ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
          </button>
        </div>

        {/* 分隔线 */}
        <div className="w-px h-6 bg-gray-600"></div>

        {/* 通知 */}
        <button className="relative p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
          <Bell className="w-4 h-4" />
          <span className="absolute -top-1 -right-1 w-3 h-3 bg-primary-500 rounded-full text-xs flex items-center justify-center text-white">
            2
          </span>
        </button>

        {/* 用户菜单 */}
        <div className="flex items-center space-x-3">
          <div className="text-right">
            <div className="text-sm font-medium text-gray-200">张工程师</div>
            <div className="text-xs text-gray-400">道路设计师</div>
          </div>
          <button className="w-8 h-8 bg-gradient-to-br from-primary-400 to-primary-600 rounded-full flex items-center justify-center text-white font-medium text-sm hover:shadow-glow transition-all duration-200">
            <User className="w-4 h-4" />
          </button>
        </div>
      </div>
    </header>
  )
}

export default Header
