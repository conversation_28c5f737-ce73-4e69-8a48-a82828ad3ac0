"""
道路数据模型
"""

from sqlalchemy import Column, String, Float, Integer, ForeignKey, Enum, JSON
from sqlalchemy.orm import relationship
import enum

from .base import BaseModel


class RoadType(str, enum.Enum):
    """道路类型枚举"""
    MAIN = "main"          # 主干道
    SECONDARY = "secondary" # 次干道
    ACCESS = "access"      # 通道
    HAUL = "haul"         # 运输道路


class RoadStatus(str, enum.Enum):
    """道路状态枚举"""
    DRAFT = "draft"        # 草稿
    DESIGNED = "designed"  # 已设计
    APPROVED = "approved"  # 已批准


class SurfaceType(str, enum.Enum):
    """路面类型枚举"""
    ASPHALT = "asphalt"    # 沥青
    CONCRETE = "concrete"  # 混凝土
    GRAVEL = "gravel"      # 碎石
    DIRT = "dirt"          # 土路


class Road(BaseModel):
    """道路模型"""
    __tablename__ = "roads"
    
    name = Column(String(200), nullable=False, index=True)
    type = Column(Enum(RoadType), default=RoadType.SECONDARY, nullable=False)
    width = Column(Float, nullable=False)  # 道路宽度(米)
    length = Column(Float, default=0.0, nullable=False)  # 道路长度(米)
    max_grade = Column(Float, nullable=False)  # 最大坡度(%)
    min_radius = Column(Float, nullable=False)  # 最小转弯半径(米)
    surface_type = Column(Enum(SurfaceType), default=SurfaceType.GRAVEL, nullable=False)
    status = Column(Enum(RoadStatus), default=RoadStatus.DRAFT, nullable=False)
    
    # 几何数据 (存储为JSON)
    coordinates = Column(JSON, nullable=True)  # 坐标点列表
    elevation_profile = Column(JSON, nullable=True)  # 高程剖面
    
    # 设计参数
    design_speed = Column(Float, default=30.0, nullable=False)  # 设计速度(km/h)
    load_capacity = Column(Float, default=50.0, nullable=False)  # 载重能力(吨)
    drainage_type = Column(String(50), default="surface", nullable=False)  # 排水类型
    shoulder_width = Column(Float, default=1.0, nullable=False)  # 路肩宽度(米)
    
    # 外键
    project_id = Column(Integer, ForeignKey('projects.id'), nullable=False)
    
    # 关联关系
    project = relationship("Project", back_populates="roads")
    
    @property
    def estimated_cost(self) -> float:
        """估算成本"""
        # 简单的成本估算公式
        base_cost_per_meter = {
            SurfaceType.DIRT: 100,
            SurfaceType.GRAVEL: 300,
            SurfaceType.ASPHALT: 800,
            SurfaceType.CONCRETE: 1200
        }
        return self.length * self.width * base_cost_per_meter.get(self.surface_type, 300)
    
    @property
    def construction_time_days(self) -> int:
        """估算施工时间(天)"""
        # 简单的时间估算公式
        meters_per_day = {
            SurfaceType.DIRT: 200,
            SurfaceType.GRAVEL: 150,
            SurfaceType.ASPHALT: 100,
            SurfaceType.CONCRETE: 80
        }
        daily_capacity = meters_per_day.get(self.surface_type, 150)
        return max(1, int(self.length / daily_capacity))
