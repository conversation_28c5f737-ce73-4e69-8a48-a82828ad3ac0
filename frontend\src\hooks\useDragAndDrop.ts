import { useState, useCallback, useRef } from 'react'

interface DragState {
  isDragging: boolean
  draggedItem: any
  dragOffset: { x: number; y: number }
  dropTarget: any
}

interface DragHandlers {
  onDragStart: (event: React.DragEvent, item: any) => void
  onDragEnd: (event: React.DragEvent) => void
  onDragOver: (event: React.DragEvent) => void
  onDrop: (event: React.DragEvent, target: any) => void
  onDragEnter: (event: React.DragEvent, target: any) => void
  onDragLeave: (event: React.DragEvent) => void
}

interface UseDragAndDropOptions {
  onItemDrop?: (draggedItem: any, dropTarget: any) => void
  onItemMove?: (draggedItem: any, newPosition: { x: number; y: number }) => void
  acceptedTypes?: string[]
  enableFileUpload?: boolean
  onFilesDrop?: (files: FileList) => void
}

export const useDragAndDrop = (options: UseDragAndDropOptions = {}) => {
  const [dragState, setDragState] = useState<DragState>({
    isDragging: false,
    draggedItem: null,
    dragOffset: { x: 0, y: 0 },
    dropTarget: null
  })

  const dragCounter = useRef(0)

  const handleDragStart = useCallback((event: React.DragEvent, item: any) => {
    const rect = (event.target as HTMLElement).getBoundingClientRect()
    const offset = {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top
    }

    setDragState({
      isDragging: true,
      draggedItem: item,
      dragOffset: offset,
      dropTarget: null
    })

    // 设置拖拽数据
    event.dataTransfer.setData('application/json', JSON.stringify(item))
    event.dataTransfer.effectAllowed = 'move'

    // 设置拖拽图像
    if (event.target instanceof HTMLElement) {
      const dragImage = event.target.cloneNode(true) as HTMLElement
      dragImage.style.opacity = '0.5'
      event.dataTransfer.setDragImage(dragImage, offset.x, offset.y)
    }
  }, [])

  const handleDragEnd = useCallback((event: React.DragEvent) => {
    setDragState({
      isDragging: false,
      draggedItem: null,
      dragOffset: { x: 0, y: 0 },
      dropTarget: null
    })
    dragCounter.current = 0
  }, [])

  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault()
    event.dataTransfer.dropEffect = 'move'
  }, [])

  const handleDragEnter = useCallback((event: React.DragEvent, target: any) => {
    event.preventDefault()
    dragCounter.current++
    
    setDragState(prev => ({
      ...prev,
      dropTarget: target
    }))
  }, [])

  const handleDragLeave = useCallback((event: React.DragEvent) => {
    dragCounter.current--
    
    if (dragCounter.current === 0) {
      setDragState(prev => ({
        ...prev,
        dropTarget: null
      }))
    }
  }, [])

  const handleDrop = useCallback((event: React.DragEvent, target: any) => {
    event.preventDefault()
    dragCounter.current = 0

    // 处理文件拖拽
    if (options.enableFileUpload && event.dataTransfer.files.length > 0) {
      if (options.onFilesDrop) {
        options.onFilesDrop(event.dataTransfer.files)
      }
      return
    }

    // 处理项目拖拽
    try {
      const draggedData = event.dataTransfer.getData('application/json')
      if (draggedData) {
        const draggedItem = JSON.parse(draggedData)
        
        if (options.onItemDrop) {
          options.onItemDrop(draggedItem, target)
        }

        // 如果是位置移动
        if (options.onItemMove) {
          const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
          const newPosition = {
            x: event.clientX - rect.left - dragState.dragOffset.x,
            y: event.clientY - rect.top - dragState.dragOffset.y
          }
          options.onItemMove(draggedItem, newPosition)
        }
      }
    } catch (error) {
      console.error('拖拽数据解析失败:', error)
    }

    setDragState({
      isDragging: false,
      draggedItem: null,
      dragOffset: { x: 0, y: 0 },
      dropTarget: null
    })
  }, [dragState.dragOffset, options])

  const handlers: DragHandlers = {
    onDragStart: handleDragStart,
    onDragEnd: handleDragEnd,
    onDragOver: handleDragOver,
    onDrop: handleDrop,
    onDragEnter: handleDragEnter,
    onDragLeave: handleDragLeave
  }

  return {
    dragState,
    handlers,
    isDragging: dragState.isDragging,
    draggedItem: dragState.draggedItem,
    dropTarget: dragState.dropTarget
  }
}

// 文件拖拽专用Hook
export const useFileDrop = (onFilesDrop: (files: FileList) => void, acceptedTypes?: string[]) => {
  const [isDragOver, setIsDragOver] = useState(false)
  const dragCounter = useRef(0)

  const handleDragEnter = useCallback((event: React.DragEvent) => {
    event.preventDefault()
    dragCounter.current++
    setIsDragOver(true)
  }, [])

  const handleDragLeave = useCallback((event: React.DragEvent) => {
    dragCounter.current--
    if (dragCounter.current === 0) {
      setIsDragOver(false)
    }
  }, [])

  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault()
  }, [])

  const handleDrop = useCallback((event: React.DragEvent) => {
    event.preventDefault()
    dragCounter.current = 0
    setIsDragOver(false)

    const files = event.dataTransfer.files
    if (files.length > 0) {
      // 过滤文件类型
      if (acceptedTypes && acceptedTypes.length > 0) {
        const filteredFiles = Array.from(files).filter(file => 
          acceptedTypes.some(type => file.type.includes(type) || file.name.toLowerCase().endsWith(type))
        )
        
        if (filteredFiles.length > 0) {
          const dataTransfer = new DataTransfer()
          filteredFiles.forEach(file => dataTransfer.items.add(file))
          onFilesDrop(dataTransfer.files)
        }
      } else {
        onFilesDrop(files)
      }
    }
  }, [onFilesDrop, acceptedTypes])

  return {
    isDragOver,
    dragHandlers: {
      onDragEnter: handleDragEnter,
      onDragLeave: handleDragLeave,
      onDragOver: handleDragOver,
      onDrop: handleDrop
    }
  }
}
