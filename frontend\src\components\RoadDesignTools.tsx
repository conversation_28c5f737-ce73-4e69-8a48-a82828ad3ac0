import React, { useState } from 'react'
import {
  MousePointer,
  Move,
  Pencil,
  Ruler,
  Square,
  Circle,
  Trash2,
  Save,
  Undo,
  Redo,
  Settings
} from 'lucide-react'
import { useRoadStore } from '../stores/useRoadStore'
import { Button } from './ui/Button'
import { Input } from './ui/Input'
import { Modal, ModalBody, ModalFooter, ModalHeader } from './ui/Modal'

interface Tool {
  id: string
  icon: React.ComponentType<any>
  label: string
  description: string
  shortcut?: string
}

export const RoadDesignTools: React.FC = () => {
  const {
    selectedTool,
    setSelectedTool,
    designParams,
    updateDesignParams,
    isDrawing,
    startDrawing,
    stopDrawing,
    clearCoordinates,
    drawingCoordinates
  } = useRoadStore()

  const [showSettings, setShowSettings] = useState(false)
  const [tempParams, setTempParams] = useState(designParams)

  const tools: Tool[] = [
    {
      id: 'pointer',
      icon: MousePointer,
      label: '选择',
      description: '选择和编辑道路',
      shortcut: 'V'
    },
    {
      id: 'move',
      icon: Move,
      label: '移动',
      description: '移动视图',
      shortcut: 'M'
    },
    {
      id: 'draw',
      icon: Pencil,
      label: '绘制道路',
      description: '绘制新的道路路径',
      shortcut: 'D'
    },
    {
      id: 'measure',
      icon: Ruler,
      label: '测量',
      description: '测量距离和面积',
      shortcut: 'R'
    },
    {
      id: 'rectangle',
      icon: Square,
      label: '矩形',
      description: '绘制矩形区域',
      shortcut: 'Q'
    },
    {
      id: 'circle',
      icon: Circle,
      label: '圆形',
      description: '绘制圆形区域',
      shortcut: 'C'
    }
  ]

  const handleToolSelect = (toolId: string) => {
    setSelectedTool(toolId)
    
    if (toolId === 'draw') {
      if (!isDrawing) {
        startDrawing()
      }
    } else {
      if (isDrawing) {
        stopDrawing()
      }
    }
  }

  const handleClearDrawing = () => {
    clearCoordinates()
    if (isDrawing) {
      stopDrawing()
    }
  }

  const handleSaveSettings = () => {
    updateDesignParams(tempParams)
    setShowSettings(false)
  }

  const handleCancelSettings = () => {
    setTempParams(designParams)
    setShowSettings(false)
  }

  // 键盘快捷键处理
  React.useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) return

      const tool = tools.find(t => t.shortcut?.toLowerCase() === e.key.toLowerCase())
      if (tool) {
        e.preventDefault()
        handleToolSelect(tool.id)
      }
    }

    window.addEventListener('keydown', handleKeyPress)
    return () => window.removeEventListener('keydown', handleKeyPress)
  }, [tools])

  return (
    <>
      <div className="bg-dark-800 border border-gray-700 rounded-lg p-4 space-y-4">
        {/* 工具栏标题 */}
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-white">设计工具</h3>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowSettings(true)}
          >
            <Settings className="w-4 h-4" />
          </Button>
        </div>

        {/* 主要工具 */}
        <div className="grid grid-cols-2 gap-2">
          {tools.map((tool) => (
            <Button
              key={tool.id}
              variant={selectedTool === tool.id ? 'primary' : 'ghost'}
              size="sm"
              onClick={() => handleToolSelect(tool.id)}
              className="flex flex-col items-center p-3 h-auto"
              title={`${tool.description} (${tool.shortcut})`}
            >
              <tool.icon className="w-5 h-5 mb-1" />
              <span className="text-xs">{tool.label}</span>
            </Button>
          ))}
        </div>

        {/* 绘制状态 */}
        {isDrawing && (
          <div className="bg-primary-500/10 border border-primary-500/30 rounded-lg p-3">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-primary-400 font-medium">
                正在绘制道路
              </span>
              <span className="text-xs text-gray-400">
                {drawingCoordinates.length} 个点
              </span>
            </div>
            <div className="flex space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={stopDrawing}
                className="flex-1"
              >
                完成
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClearDrawing}
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="space-y-2">
          <div className="grid grid-cols-2 gap-2">
            <Button variant="ghost" size="sm" disabled>
              <Undo className="w-4 h-4 mr-1" />
              撤销
            </Button>
            <Button variant="ghost" size="sm" disabled>
              <Redo className="w-4 h-4 mr-1" />
              重做
            </Button>
          </div>
          
          <Button variant="secondary" size="sm" className="w-full">
            <Save className="w-4 h-4 mr-2" />
            保存设计
          </Button>
        </div>

        {/* 当前参数显示 */}
        <div className="bg-dark-700 rounded-lg p-3 space-y-2">
          <h4 className="text-sm font-medium text-gray-300">当前参数</h4>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div>
              <span className="text-gray-400">宽度:</span>
              <span className="text-white ml-1">{designParams.width}m</span>
            </div>
            <div>
              <span className="text-gray-400">坡度:</span>
              <span className="text-white ml-1">{designParams.maxGrade}%</span>
            </div>
            <div>
              <span className="text-gray-400">半径:</span>
              <span className="text-white ml-1">{designParams.minRadius}m</span>
            </div>
            <div>
              <span className="text-gray-400">路面:</span>
              <span className="text-white ml-1">
                {designParams.surfaceType === 'asphalt' ? '沥青' :
                 designParams.surfaceType === 'concrete' ? '混凝土' :
                 designParams.surfaceType === 'gravel' ? '碎石' : '土路'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* 设置模态框 */}
      <Modal
        isOpen={showSettings}
        onClose={handleCancelSettings}
        title="道路设计参数"
        size="md"
      >
        <ModalBody>
          <div className="space-y-4">
            <Input
              label="道路宽度 (m)"
              type="number"
              value={tempParams.width}
              onChange={(e) => setTempParams({
                ...tempParams,
                width: Number(e.target.value)
              })}
              min="3"
              max="20"
              step="0.5"
            />

            <Input
              label="最大坡度 (%)"
              type="number"
              value={tempParams.maxGrade}
              onChange={(e) => setTempParams({
                ...tempParams,
                maxGrade: Number(e.target.value)
              })}
              min="1"
              max="15"
              step="0.5"
            />

            <Input
              label="最小转弯半径 (m)"
              type="number"
              value={tempParams.minRadius}
              onChange={(e) => setTempParams({
                ...tempParams,
                minRadius: Number(e.target.value)
              })}
              min="10"
              max="100"
              step="5"
            />

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-300">
                路面类型
              </label>
              <select
                value={tempParams.surfaceType}
                onChange={(e) => setTempParams({
                  ...tempParams,
                  surfaceType: e.target.value
                })}
                className="w-full px-3 py-2 bg-dark-800 border border-gray-600 rounded-lg text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="asphalt">沥青路面</option>
                <option value="concrete">混凝土路面</option>
                <option value="gravel">碎石路面</option>
                <option value="dirt">土路</option>
              </select>
            </div>

            <Input
              label="设计速度 (km/h)"
              type="number"
              value={tempParams.designSpeed}
              onChange={(e) => setTempParams({
                ...tempParams,
                designSpeed: Number(e.target.value)
              })}
              min="10"
              max="80"
              step="5"
            />

            <Input
              label="载重能力 (吨)"
              type="number"
              value={tempParams.loadCapacity}
              onChange={(e) => setTempParams({
                ...tempParams,
                loadCapacity: Number(e.target.value)
              })}
              min="10"
              max="200"
              step="10"
            />
          </div>
        </ModalBody>

        <ModalFooter>
          <Button variant="ghost" onClick={handleCancelSettings}>
            取消
          </Button>
          <Button variant="primary" onClick={handleSaveSettings}>
            保存
          </Button>
        </ModalFooter>
      </Modal>
    </>
  )
}
