import React, { useState, useEffect, useRef } from 'react'
import { Eye, EyeOff, Settings, Layers, BarChart3 } from 'lucide-react'
import { useRoadStore } from '../stores/useRoadStore'
import { RoadAnalysisService } from '../services/roadAnalysisService'
import { Button } from './ui/Button'

interface PreviewSettings {
  showElevationProfile: boolean
  showGradeColors: boolean
  showCurveRadius: boolean
  showCrossSection: boolean
  animateConstruction: boolean
  previewQuality: 'low' | 'medium' | 'high'
}

export const RealTimePreview: React.FC = () => {
  const { drawingCoordinates, designParams, selectedTool } = useRoadStore()
  const [isVisible, setIsVisible] = useState(true)
  const [previewSettings, setPreviewSettings] = useState<PreviewSettings>({
    showElevationProfile: true,
    showGradeColors: true,
    showCurveRadius: false,
    showCrossSection: false,
    animateConstruction: false,
    previewQuality: 'medium'
  })
  const [previewData, setPreviewData] = useState<any>(null)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const canvasRef = useRef<HTMLCanvasElement>(null)

  // 实时分析道路数据
  useEffect(() => {
    if (drawingCoordinates.length >= 2) {
      analyzeRoadPreview()
    } else {
      setPreviewData(null)
    }
  }, [drawingCoordinates, designParams])

  const analyzeRoadPreview = async () => {
    setIsAnalyzing(true)
    try {
      const analysis = RoadAnalysisService.analyzeGeometry(drawingCoordinates)
      const validation = RoadAnalysisService.validateDesign(drawingCoordinates, designParams)
      
      setPreviewData({
        analysis,
        validation,
        segments: analysis.segments,
        elevationProfile: analysis.elevationProfile
      })
    } catch (error) {
      console.error('预览分析失败:', error)
    } finally {
      setIsAnalyzing(false)
    }
  }

  // 绘制高程剖面图
  const drawElevationProfile = () => {
    const canvas = canvasRef.current
    if (!canvas || !previewData?.elevationProfile) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    const { width, height } = canvas
    ctx.clearRect(0, 0, width, height)

    const profile = previewData.elevationProfile
    if (profile.length < 2) return

    // 计算缩放比例
    const maxDistance = Math.max(...profile.map((p: any) => p.distance))
    const minElevation = Math.min(...profile.map((p: any) => p.elevation))
    const maxElevation = Math.max(...profile.map((p: any) => p.elevation))
    
    const scaleX = (width - 40) / maxDistance
    const scaleY = (height - 40) / (maxElevation - minElevation || 1)

    // 绘制坐标轴
    ctx.strokeStyle = '#6b7280'
    ctx.lineWidth = 1
    ctx.beginPath()
    ctx.moveTo(20, height - 20)
    ctx.lineTo(width - 20, height - 20) // X轴
    ctx.moveTo(20, 20)
    ctx.lineTo(20, height - 20) // Y轴
    ctx.stroke()

    // 绘制高程线
    ctx.strokeStyle = '#f59e0b'
    ctx.lineWidth = 2
    ctx.beginPath()

    profile.forEach((point: any, index: number) => {
      const x = 20 + point.distance * scaleX
      const y = height - 20 - (point.elevation - minElevation) * scaleY

      if (index === 0) {
        ctx.moveTo(x, y)
      } else {
        ctx.lineTo(x, y)
      }
    })
    ctx.stroke()

    // 绘制坡度颜色编码
    if (previewSettings.showGradeColors) {
      profile.forEach((point: any, index: number) => {
        if (index === 0) return

        const grade = Math.abs(point.grade)
        let color = '#10b981' // 绿色 - 正常坡度
        
        if (grade > designParams.maxGrade) {
          color = '#ef4444' // 红色 - 超出限制
        } else if (grade > designParams.maxGrade * 0.8) {
          color = '#f59e0b' // 黄色 - 接近限制
        }

        const x1 = 20 + profile[index - 1].distance * scaleX
        const y1 = height - 20 - (profile[index - 1].elevation - minElevation) * scaleY
        const x2 = 20 + point.distance * scaleX
        const y2 = height - 20 - (point.elevation - minElevation) * scaleY

        ctx.strokeStyle = color
        ctx.lineWidth = 3
        ctx.beginPath()
        ctx.moveTo(x1, y1)
        ctx.lineTo(x2, y2)
        ctx.stroke()
      })
    }

    // 绘制标签
    ctx.fillStyle = '#f1f5f9'
    ctx.font = '12px Inter'
    ctx.fillText(`距离: ${(maxDistance / 1000).toFixed(1)} km`, 25, height - 5)
    ctx.fillText(`高程: ${minElevation.toFixed(0)}m - ${maxElevation.toFixed(0)}m`, 25, 15)
  }

  // 绘制横断面
  const drawCrossSection = (segmentIndex: number) => {
    // 实现横断面绘制逻辑
    console.log('绘制横断面:', segmentIndex)
  }

  // 更新画布
  useEffect(() => {
    if (previewSettings.showElevationProfile) {
      drawElevationProfile()
    }
  }, [previewData, previewSettings, designParams])

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-20">
        <Button
          variant="primary"
          size="sm"
          onClick={() => setIsVisible(true)}
        >
          <Eye className="w-4 h-4 mr-2" />
          显示预览
        </Button>
      </div>
    )
  }

  return (
    <div className="fixed bottom-4 right-4 w-96 bg-dark-800/95 backdrop-blur-sm border border-gray-700 rounded-lg shadow-xl z-20">
      {/* 头部 */}
      <div className="flex items-center justify-between p-3 border-b border-gray-700">
        <div className="flex items-center space-x-2">
          <BarChart3 className="w-4 h-4 text-primary-400" />
          <span className="text-sm font-medium text-white">实时预览</span>
          {isAnalyzing && (
            <div className="w-3 h-3 border border-primary-500 border-t-transparent rounded-full animate-spin"></div>
          )}
        </div>
        
        <div className="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {/* 打开设置 */}}
          >
            <Settings className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsVisible(false)}
          >
            <EyeOff className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* 内容 */}
      <div className="p-3 space-y-3">
        {!previewData ? (
          <div className="text-center py-8 text-gray-400">
            <BarChart3 className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">开始绘制道路以查看预览</p>
          </div>
        ) : (
          <>
            {/* 基本信息 */}
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="bg-dark-700 rounded p-2">
                <div className="text-gray-400">长度</div>
                <div className="text-white font-medium">
                  {(previewData.analysis.totalLength / 1000).toFixed(2)} km
                </div>
              </div>
              <div className="bg-dark-700 rounded p-2">
                <div className="text-gray-400">平均坡度</div>
                <div className="text-white font-medium">
                  {previewData.analysis.grades.average.toFixed(1)}%
                </div>
              </div>
              <div className="bg-dark-700 rounded p-2">
                <div className="text-gray-400">最大坡度</div>
                <div className={`font-medium ${
                  previewData.analysis.grades.max > designParams.maxGrade 
                    ? 'text-red-400' : 'text-white'
                }`}>
                  {previewData.analysis.grades.max.toFixed(1)}%
                </div>
              </div>
              <div className="bg-dark-700 rounded p-2">
                <div className="text-gray-400">转弯数</div>
                <div className="text-white font-medium">
                  {previewData.analysis.curves.length}
                </div>
              </div>
            </div>

            {/* 验证状态 */}
            {previewData.validation && (
              <div className="space-y-1">
                {previewData.validation.violations.length > 0 && (
                  <div className="bg-red-500/10 border border-red-500/30 rounded p-2">
                    <div className="text-xs text-red-400 font-medium mb-1">设计违规</div>
                    {previewData.validation.violations.slice(0, 2).map((violation: string, index: number) => (
                      <div key={index} className="text-xs text-red-300">• {violation}</div>
                    ))}
                  </div>
                )}
                
                {previewData.validation.warnings.length > 0 && (
                  <div className="bg-yellow-500/10 border border-yellow-500/30 rounded p-2">
                    <div className="text-xs text-yellow-400 font-medium mb-1">设计警告</div>
                    {previewData.validation.warnings.slice(0, 2).map((warning: string, index: number) => (
                      <div key={index} className="text-xs text-yellow-300">• {warning}</div>
                    ))}
                  </div>
                )}
                
                {previewData.validation.valid && (
                  <div className="bg-green-500/10 border border-green-500/30 rounded p-2">
                    <div className="text-xs text-green-400 font-medium">✓ 设计符合规范</div>
                  </div>
                )}
              </div>
            )}

            {/* 高程剖面图 */}
            {previewSettings.showElevationProfile && (
              <div className="bg-dark-700 rounded p-2">
                <div className="text-xs text-gray-400 mb-2">高程剖面</div>
                <canvas
                  ref={canvasRef}
                  width={320}
                  height={120}
                  className="w-full h-auto border border-gray-600 rounded"
                />
              </div>
            )}

            {/* 快速操作 */}
            <div className="flex space-x-2">
              <Button
                variant="ghost"
                size="sm"
                className="flex-1 text-xs"
                onClick={() => {/* 优化路径 */}}
              >
                优化路径
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="flex-1 text-xs"
                onClick={() => {/* 生成报告 */}}
              >
                生成报告
              </Button>
            </div>
          </>
        )}
      </div>
    </div>
  )
}
