🎯 露天矿山道路设计系统 - 运行指南

========================================
  方法1: 手动打开演示页面 (推荐)
========================================

1. 找到项目文件夹中的以下文件：
   📄 simple-demo.html

2. 右键点击该文件，选择"打开方式"

3. 选择您的浏览器打开：
   - Chrome (推荐)
   - Edge 
   - Firefox
   - Safari

4. 如果没有"打开方式"选项：
   - 直接双击 simple-demo.html 文件
   - 系统会用默认浏览器打开

========================================
  方法2: 复制文件路径到浏览器
========================================

1. 右键点击 simple-demo.html 文件
2. 选择"复制为路径"
3. 打开任意浏览器
4. 在地址栏粘贴路径并按回车

示例路径格式：
file:///C:/Users/<USER>/openpit-road-design03/simple-demo.html

========================================
  方法3: 拖拽到浏览器
========================================

1. 打开任意浏览器窗口
2. 将 simple-demo.html 文件直接拖拽到浏览器窗口中
3. 文件会自动在浏览器中打开

========================================
  演示页面功能说明
========================================

打开后您将看到：

🏔️ 顶部标题栏
   - 项目名称和用户信息

🛠️ 左侧工具面板
   - 设计工具：选择、移动、绘制、测量
   - 道路参数：宽度、坡度、转弯半径
   - 分析结果：长度、坡度、成本统计
   - 项目操作：保存、导出功能

🗺️ 中央主视图
   - 三维地形视图区域
   - 交互式工具栏
   - 坐标和状态信息
   - 道路路径动画演示

📊 右下角预览面板
   - 实时分析数据
   - 设计验证状态

========================================
  交互操作说明
========================================

✅ 可以点击的元素：
   - 左侧所有工具按钮
   - 参数输入框和下拉菜单
   - "保存项目"和"导出报告"按钮
   - 顶部工具栏按钮

✅ 实时更新功能：
   - 修改参数时，分析结果会自动更新
   - 坐标信息每2秒自动刷新
   - 道路路径有发光动画效果

✅ 状态反馈：
   - 工具选择时会高亮显示
   - 参数修改会触发重新计算
   - 按钮点击有视觉反馈

========================================
  故障排除
========================================

❌ 如果页面无法打开：
   1. 确认浏览器已安装且为最新版本
   2. 尝试不同的浏览器
   3. 检查文件是否完整存在

❌ 如果页面显示异常：
   1. 刷新页面 (F5)
   2. 清除浏览器缓存
   3. 尝试无痕/隐私模式

❌ 如果功能无响应：
   1. 按F12打开开发者工具
   2. 查看Console标签页的错误信息
   3. 确认JavaScript已启用

========================================
  完整版本安装
========================================

当前是演示版本，如需完整功能：

1. 安装Node.js 18+ (前端开发)
   下载：https://nodejs.org/

2. 安装Python 3.11+ (后端开发)  
   下载：https://python.org/

3. 参考 INSTALLATION.md 文件
   详细的安装和配置说明

4. 或使用Docker部署
   docker-compose up -d

========================================
  技术支持
========================================

📧 如需帮助：
   - 查看 README.md 项目文档
   - 查看 PROJECT_STATUS.md 状态报告
   - 查看 INSTALLATION.md 安装指南

🔧 开发环境：
   - 前端：React + TypeScript + Vite
   - 后端：FastAPI + Python
   - 数据库：PostgreSQL + Redis
   - 3D引擎：Cesium

========================================

祝您使用愉快！🚀

如果成功打开了演示页面，您就可以体验完整的界面设计和交互功能了！
