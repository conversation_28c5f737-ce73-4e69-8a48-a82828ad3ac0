import { useEffect, useCallback } from 'react'

interface ShortcutConfig {
  key: string
  ctrl?: boolean
  shift?: boolean
  alt?: boolean
  action: () => void
  description: string
  preventDefault?: boolean
}

export const useKeyboardShortcuts = (shortcuts: ShortcutConfig[]) => {
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // 忽略在输入框中的按键
    const target = event.target as HTMLElement
    if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.contentEditable === 'true') {
      return
    }

    for (const shortcut of shortcuts) {
      const keyMatch = event.key.toLowerCase() === shortcut.key.toLowerCase()
      const ctrlMatch = shortcut.ctrl ? event.ctrlKey || event.metaKey : !event.ctrlKey && !event.metaKey
      const shiftMatch = shortcut.shift ? event.shiftKey : !event.shiftKey
      const altMatch = shortcut.alt ? event.altKey : !event.altKey

      if (keyMatch && ctrlMatch && shiftMatch && altMatch) {
        if (shortcut.preventDefault !== false) {
          event.preventDefault()
        }
        shortcut.action()
        break
      }
    }
  }, [shortcuts])

  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [handleKeyDown])

  return shortcuts
}

// 预定义的快捷键配置
export const defaultShortcuts: ShortcutConfig[] = [
  {
    key: 's',
    ctrl: true,
    action: () => console.log('保存项目'),
    description: '保存项目'
  },
  {
    key: 'z',
    ctrl: true,
    action: () => console.log('撤销'),
    description: '撤销'
  },
  {
    key: 'y',
    ctrl: true,
    action: () => console.log('重做'),
    description: '重做'
  },
  {
    key: 'n',
    ctrl: true,
    action: () => console.log('新建项目'),
    description: '新建项目'
  },
  {
    key: 'o',
    ctrl: true,
    action: () => console.log('打开项目'),
    description: '打开项目'
  },
  {
    key: 'v',
    action: () => console.log('选择工具'),
    description: '选择工具'
  },
  {
    key: 'd',
    action: () => console.log('绘制工具'),
    description: '绘制工具'
  },
  {
    key: 'm',
    action: () => console.log('移动工具'),
    description: '移动工具'
  },
  {
    key: 'r',
    action: () => console.log('测量工具'),
    description: '测量工具'
  },
  {
    key: 'Escape',
    action: () => console.log('取消当前操作'),
    description: '取消当前操作'
  },
  {
    key: 'Delete',
    action: () => console.log('删除选中项'),
    description: '删除选中项'
  },
  {
    key: 'F11',
    action: () => {
      if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen()
      } else {
        document.exitFullscreen()
      }
    },
    description: '切换全屏'
  }
]
