# 露天矿山道路设计软件

一款基于Python和Cesium的现代化露天矿山道路设计Web应用。

## 功能特性

- 🗺️ **三维地形可视化** - 基于Cesium的高性能三维地球引擎
- 🛣️ **智能道路设计** - 专业的道路设计算法和优化工具
- 🎨 **现代化界面** - 简洁的黑灰黄白主色调设计
- ⚡ **高效交互** - 拖拽操作、实时预览、快捷键支持
- 💾 **数据管理** - 项目保存、版本控制、数据导入导出

## 技术栈

### 后端
- **FastAPI** - 现代化Python Web框架
- **SQLAlchemy** - ORM数据库操作
- **PostgreSQL** - 关系型数据库
- **Pydantic** - 数据验证和序列化

### 前端
- **React 18** - 用户界面框架
- **TypeScript** - 类型安全的JavaScript
- **Vite** - 快速构建工具
- **Tailwind CSS** - 实用优先的CSS框架

### GIS平台
- **Cesium.js** - 三维地球和地图引擎
- **Turf.js** - 地理空间分析库

## 项目结构

```
openpit-road-design/
├── backend/                 # Python后端服务
│   ├── app/
│   │   ├── api/            # API路由
│   │   ├── core/           # 核心配置
│   │   ├── models/         # 数据模型
│   │   ├── services/       # 业务逻辑
│   │   └── utils/          # 工具函数
│   ├── tests/              # 后端测试
│   └── requirements.txt    # Python依赖
├── frontend/               # React前端应用
│   ├── src/
│   │   ├── components/     # React组件
│   │   ├── pages/          # 页面组件
│   │   ├── services/       # API服务
│   │   ├── utils/          # 工具函数
│   │   └── styles/         # 样式文件
│   ├── public/             # 静态资源
│   └── package.json        # 前端依赖
├── docs/                   # 项目文档
├── docker-compose.yml      # Docker配置
└── README.md              # 项目说明
```

## 快速开始

### 环境要求
- Python 3.9+
- Node.js 16+
- PostgreSQL 13+

### 安装依赖

#### 后端
```bash
cd backend
pip install -r requirements.txt
```

#### 前端
```bash
cd frontend
npm install
```

### 运行开发服务器

#### 后端
```bash
cd backend
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### 前端
```bash
cd frontend
npm run dev
```

访问 http://localhost:5173 查看应用。

## 开发指南

### 代码规范
- 后端遵循PEP 8规范
- 前端使用ESLint + Prettier
- 提交信息遵循Conventional Commits

### 测试
```bash
# 后端测试
cd backend && pytest

# 前端测试
cd frontend && npm test
```

## 许可证

MIT License
