import React, { useState, useRef, useEffect } from 'react'
import {
  <PERSON><PERSON>ointer,
  Move,
  Pencil,
  Ruler,
  Square,
  Circle,
  Undo,
  Redo,
  Save,
  Settings,
  Maximize2,
  Minimize2,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Grid,
  Eye,
  EyeOff,
  HelpCircle
} from 'lucide-react'
import { useRoadStore } from '../stores/useRoadStore'
import { useKeyboardShortcuts } from '../hooks/useKeyboardShortcuts'
import { Button } from './ui/Button'

interface Tool {
  id: string
  icon: React.ComponentType<any>
  label: string
  shortcut?: string
  action: () => void
  active?: boolean
  disabled?: boolean
}

interface ToolbarProps {
  onToolChange?: (toolId: string) => void
  onViewAction?: (action: string) => void
  className?: string
}

export const InteractiveToolbar: React.FC<ToolbarProps> = ({
  onToolChange,
  onViewAction,
  className = ''
}) => {
  const { selectedTool, setSelectedTool, isDrawing, startDrawing, stopDrawing } = useRoadStore()
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [showGrid, setShowGrid] = useState(false)
  const [showHelp, setShowHelp] = useState(false)
  const [undoStack, setUndoStack] = useState<any[]>([])
  const [redoStack, setRedoStack] = useState<any[]>([])

  // 工具定义
  const tools: Tool[] = [
    {
      id: 'pointer',
      icon: MousePointer,
      label: '选择',
      shortcut: 'V',
      action: () => handleToolSelect('pointer'),
      active: selectedTool === 'pointer'
    },
    {
      id: 'move',
      icon: Move,
      label: '移动',
      shortcut: 'M',
      action: () => handleToolSelect('move'),
      active: selectedTool === 'move'
    },
    {
      id: 'draw',
      icon: Pencil,
      label: '绘制',
      shortcut: 'D',
      action: () => handleToolSelect('draw'),
      active: selectedTool === 'draw'
    },
    {
      id: 'measure',
      icon: Ruler,
      label: '测量',
      shortcut: 'R',
      action: () => handleToolSelect('measure'),
      active: selectedTool === 'measure'
    },
    {
      id: 'rectangle',
      icon: Square,
      label: '矩形',
      shortcut: 'Q',
      action: () => handleToolSelect('rectangle'),
      active: selectedTool === 'rectangle'
    },
    {
      id: 'circle',
      icon: Circle,
      label: '圆形',
      shortcut: 'C',
      action: () => handleToolSelect('circle'),
      active: selectedTool === 'circle'
    }
  ]

  // 视图操作
  const viewActions = [
    {
      id: 'zoom-in',
      icon: ZoomIn,
      label: '放大',
      shortcut: '+',
      action: () => handleViewAction('zoom-in')
    },
    {
      id: 'zoom-out',
      icon: ZoomOut,
      label: '缩小',
      shortcut: '-',
      action: () => handleViewAction('zoom-out')
    },
    {
      id: 'reset-view',
      icon: RotateCcw,
      label: '重置视图',
      shortcut: 'Home',
      action: () => handleViewAction('reset-view')
    },
    {
      id: 'fullscreen',
      icon: isFullscreen ? Minimize2 : Maximize2,
      label: isFullscreen ? '退出全屏' : '全屏',
      shortcut: 'F11',
      action: () => handleToggleFullscreen()
    }
  ]

  // 编辑操作
  const editActions = [
    {
      id: 'undo',
      icon: Undo,
      label: '撤销',
      shortcut: 'Ctrl+Z',
      action: () => handleUndo(),
      disabled: undoStack.length === 0
    },
    {
      id: 'redo',
      icon: Redo,
      label: '重做',
      shortcut: 'Ctrl+Y',
      action: () => handleRedo(),
      disabled: redoStack.length === 0
    },
    {
      id: 'save',
      icon: Save,
      label: '保存',
      shortcut: 'Ctrl+S',
      action: () => handleSave()
    }
  ]

  // 快捷键配置
  const shortcuts = [
    ...tools.map(tool => ({
      key: tool.shortcut || '',
      action: tool.action,
      description: `${tool.label} (${tool.shortcut})`
    })),
    ...viewActions.map(action => ({
      key: action.shortcut || '',
      action: action.action,
      description: `${action.label} (${action.shortcut})`,
      ctrl: action.shortcut?.includes('Ctrl'),
      preventDefault: action.id === 'fullscreen'
    })),
    ...editActions.map(action => ({
      key: action.shortcut?.split('+').pop() || '',
      action: action.action,
      description: `${action.label} (${action.shortcut})`,
      ctrl: action.shortcut?.includes('Ctrl')
    }))
  ].filter(shortcut => shortcut.key)

  useKeyboardShortcuts(shortcuts)

  // 处理工具选择
  const handleToolSelect = (toolId: string) => {
    setSelectedTool(toolId)
    
    if (toolId === 'draw') {
      if (!isDrawing) {
        startDrawing()
      }
    } else {
      if (isDrawing) {
        stopDrawing()
      }
    }

    if (onToolChange) {
      onToolChange(toolId)
    }
  }

  // 处理视图操作
  const handleViewAction = (action: string) => {
    if (onViewAction) {
      onViewAction(action)
    }
  }

  // 切换全屏
  const handleToggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen()
      setIsFullscreen(true)
    } else {
      document.exitFullscreen()
      setIsFullscreen(false)
    }
  }

  // 撤销操作
  const handleUndo = () => {
    if (undoStack.length > 0) {
      const lastAction = undoStack[undoStack.length - 1]
      setRedoStack([...redoStack, lastAction])
      setUndoStack(undoStack.slice(0, -1))
      // 执行撤销逻辑
      console.log('撤销操作:', lastAction)
    }
  }

  // 重做操作
  const handleRedo = () => {
    if (redoStack.length > 0) {
      const lastAction = redoStack[redoStack.length - 1]
      setUndoStack([...undoStack, lastAction])
      setRedoStack(redoStack.slice(0, -1))
      // 执行重做逻辑
      console.log('重做操作:', lastAction)
    }
  }

  // 保存操作
  const handleSave = () => {
    console.log('保存项目')
    // 实现保存逻辑
  }

  // 监听全屏状态变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement)
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange)
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange)
  }, [])

  return (
    <div className={`bg-dark-800/90 backdrop-blur-sm border border-gray-700 rounded-lg shadow-lg ${className}`}>
      <div className="flex items-center p-2 space-x-1">
        {/* 主要工具 */}
        <div className="flex items-center space-x-1 pr-2 border-r border-gray-600">
          {tools.map((tool) => (
            <Button
              key={tool.id}
              variant={tool.active ? 'primary' : 'ghost'}
              size="sm"
              onClick={tool.action}
              disabled={tool.disabled}
              title={`${tool.label} (${tool.shortcut})`}
              className="relative"
            >
              <tool.icon className="w-4 h-4" />
              {tool.shortcut && (
                <span className="absolute -bottom-1 -right-1 text-xs bg-gray-600 text-gray-300 px-1 rounded opacity-0 group-hover:opacity-100 transition-opacity">
                  {tool.shortcut}
                </span>
              )}
            </Button>
          ))}
        </div>

        {/* 编辑操作 */}
        <div className="flex items-center space-x-1 pr-2 border-r border-gray-600">
          {editActions.map((action) => (
            <Button
              key={action.id}
              variant="ghost"
              size="sm"
              onClick={action.action}
              disabled={action.disabled}
              title={`${action.label} (${action.shortcut})`}
            >
              <action.icon className="w-4 h-4" />
            </Button>
          ))}
        </div>

        {/* 视图操作 */}
        <div className="flex items-center space-x-1 pr-2 border-r border-gray-600">
          {viewActions.map((action) => (
            <Button
              key={action.id}
              variant="ghost"
              size="sm"
              onClick={action.action}
              title={`${action.label} (${action.shortcut})`}
            >
              <action.icon className="w-4 h-4" />
            </Button>
          ))}
        </div>

        {/* 显示选项 */}
        <div className="flex items-center space-x-1 pr-2 border-r border-gray-600">
          <Button
            variant={showGrid ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setShowGrid(!showGrid)}
            title="显示网格 (G)"
          >
            <Grid className="w-4 h-4" />
          </Button>
        </div>

        {/* 帮助 */}
        <div className="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowHelp(!showHelp)}
            title="快捷键帮助 (?)"
          >
            <HelpCircle className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* 快捷键帮助面板 */}
      {showHelp && (
        <div className="absolute top-full left-0 mt-2 bg-dark-800 border border-gray-700 rounded-lg shadow-xl p-4 z-50 min-w-80">
          <h3 className="text-sm font-semibold text-white mb-3">快捷键</h3>
          <div className="grid grid-cols-2 gap-2 text-xs">
            {shortcuts.slice(0, 12).map((shortcut, index) => (
              <div key={index} className="flex justify-between">
                <span className="text-gray-400">{shortcut.description.split('(')[0].trim()}:</span>
                <span className="text-gray-300 font-mono">
                  {shortcut.description.match(/\(([^)]+)\)/)?.[1]}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
